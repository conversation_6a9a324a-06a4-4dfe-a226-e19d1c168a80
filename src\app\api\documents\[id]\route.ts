import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { uploadToGoogleDrive, updateGoogleSheet } from '@/lib/google'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params
    const formData = await request.formData()
    const action = formData.get('action') as string

    if (action === 'sign' && session.user.role === 'admin') {
      // Admin signing document
      const signedFile = formData.get('signedFile') as File
      
      if (!signedFile) {
        return NextResponse.json({ error: 'Signed file is required' }, { status: 400 })
      }

      // Upload signed file to Google Drive
      const fileBuffer = Buffer.from(await signedFile.arrayBuffer())
      const driveResult = await uploadToGoogleDrive(
        `signed_${signedFile.name}`,
        fileBuffer,
        signedFile.type
      )

      // Update document record
      const { data: document, error } = await supabase
        .from('documents')
        .update({
          status: 'signed',
          signed_filename: signedFile.name,
          signed_file_url: driveResult.webViewLink,
          signed_drive_id: driveResult.fileId,
          admin_id: session.user.id,
          signed_at: new Date().toISOString()
        })
        .eq('id', id)
        .select(`
          *,
          user:users!documents_user_id_fkey(id, name, email)
        `)
        .single()

      if (error) {
        console.error('Error updating document:', error)
        return NextResponse.json({ error: 'Failed to update document' }, { status: 500 })
      }

      // Update Google Sheets
      try {
        await updateGoogleSheet([
          [
            new Date().toISOString(),
            document.user.name,
            document.user.email,
            document.subject,
            document.details,
            document.urgency,
            'signed',
            document.original_file_url,
            driveResult.webViewLink
          ]
        ])
      } catch (sheetError) {
        console.error('Error updating Google Sheets:', sheetError)
      }

      return NextResponse.json({ document })

    } else if (action === 'feedback' && session.user.role === 'admin') {
      // Admin providing feedback
      const feedback = formData.get('feedback') as string
      
      if (!feedback) {
        return NextResponse.json({ error: 'Feedback is required' }, { status: 400 })
      }

      const { data: document, error } = await supabase
        .from('documents')
        .update({
          status: 'feedback',
          feedback,
          admin_id: session.user.id
        })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating document:', error)
        return NextResponse.json({ error: 'Failed to update document' }, { status: 500 })
      }

      return NextResponse.json({ document })

    } else {
      return NextResponse.json({ error: 'Invalid action or insufficient permissions' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error in PUT /api/documents/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params

    let query = supabase
      .from('documents')
      .select(`
        *,
        user:users!documents_user_id_fkey(id, name, email),
        admin:users!documents_admin_id_fkey(id, name, email)
      `)
      .eq('id', id)

    // Users can only view their own documents, admins can view all
    if (session.user.role === 'user') {
      query = query.eq('user_id', session.user.id)
    }

    const { data: document, error } = await query.single()

    if (error) {
      console.error('Error fetching document:', error)
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    return NextResponse.json({ document })
  } catch (error) {
    console.error('Error in GET /api/documents/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
