"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/https-proxy-agent";
exports.ids = ["vendor-chunks/https-proxy-agent"];
exports.modules = {

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HttpsProxyAgent = void 0;\nconst net = __importStar(__webpack_require__(/*! net */ \"net\"));\nconst tls = __importStar(__webpack_require__(/*! tls */ \"tls\"));\nconst assert_1 = __importDefault(__webpack_require__(/*! assert */ \"assert\"));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst agent_base_1 = __webpack_require__(/*! agent-base */ \"(rsc)/./node_modules/agent-base/dist/index.js\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst parse_proxy_response_1 = __webpack_require__(/*! ./parse-proxy-response */ \"(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\");\nconst debug = (0, debug_1.default)('https-proxy-agent');\nconst setServernameFromNonIpHost = (options) => {\n    if (options.servername === undefined &&\n        options.host &&\n        !net.isIP(options.host)) {\n        return {\n            ...options,\n            servername: options.host,\n        };\n    }\n    return options;\n};\n/**\n * The `HttpsProxyAgent` implements an HTTP Agent subclass that connects to\n * the specified \"HTTP(s) proxy server\" in order to proxy HTTPS requests.\n *\n * Outgoing HTTP requests are first tunneled through the proxy server using the\n * `CONNECT` HTTP request method to establish a connection to the proxy server,\n * and then the proxy server connects to the destination target and issues the\n * HTTP request from the proxy server.\n *\n * `https:` requests have their socket connection upgraded to TLS once\n * the connection to the proxy server has been established.\n */\nclass HttpsProxyAgent extends agent_base_1.Agent {\n    constructor(proxy, opts) {\n        super(opts);\n        this.options = { path: undefined };\n        this.proxy = typeof proxy === 'string' ? new url_1.URL(proxy) : proxy;\n        this.proxyHeaders = opts?.headers ?? {};\n        debug('Creating new HttpsProxyAgent instance: %o', this.proxy.href);\n        // Trim off the brackets from IPv6 addresses\n        const host = (this.proxy.hostname || this.proxy.host).replace(/^\\[|\\]$/g, '');\n        const port = this.proxy.port\n            ? parseInt(this.proxy.port, 10)\n            : this.proxy.protocol === 'https:'\n                ? 443\n                : 80;\n        this.connectOpts = {\n            // Attempt to negotiate http/1.1 for proxy servers that support http/2\n            ALPNProtocols: ['http/1.1'],\n            ...(opts ? omit(opts, 'headers') : null),\n            host,\n            port,\n        };\n    }\n    /**\n     * Called when the node-core HTTP client library is creating a\n     * new HTTP request.\n     */\n    async connect(req, opts) {\n        const { proxy } = this;\n        if (!opts.host) {\n            throw new TypeError('No \"host\" provided');\n        }\n        // Create a socket connection to the proxy server.\n        let socket;\n        if (proxy.protocol === 'https:') {\n            debug('Creating `tls.Socket`: %o', this.connectOpts);\n            socket = tls.connect(setServernameFromNonIpHost(this.connectOpts));\n        }\n        else {\n            debug('Creating `net.Socket`: %o', this.connectOpts);\n            socket = net.connect(this.connectOpts);\n        }\n        const headers = typeof this.proxyHeaders === 'function'\n            ? this.proxyHeaders()\n            : { ...this.proxyHeaders };\n        const host = net.isIPv6(opts.host) ? `[${opts.host}]` : opts.host;\n        let payload = `CONNECT ${host}:${opts.port} HTTP/1.1\\r\\n`;\n        // Inject the `Proxy-Authorization` header if necessary.\n        if (proxy.username || proxy.password) {\n            const auth = `${decodeURIComponent(proxy.username)}:${decodeURIComponent(proxy.password)}`;\n            headers['Proxy-Authorization'] = `Basic ${Buffer.from(auth).toString('base64')}`;\n        }\n        headers.Host = `${host}:${opts.port}`;\n        if (!headers['Proxy-Connection']) {\n            headers['Proxy-Connection'] = this.keepAlive\n                ? 'Keep-Alive'\n                : 'close';\n        }\n        for (const name of Object.keys(headers)) {\n            payload += `${name}: ${headers[name]}\\r\\n`;\n        }\n        const proxyResponsePromise = (0, parse_proxy_response_1.parseProxyResponse)(socket);\n        socket.write(`${payload}\\r\\n`);\n        const { connect, buffered } = await proxyResponsePromise;\n        req.emit('proxyConnect', connect);\n        this.emit('proxyConnect', connect, req);\n        if (connect.statusCode === 200) {\n            req.once('socket', resume);\n            if (opts.secureEndpoint) {\n                // The proxy is connecting to a TLS server, so upgrade\n                // this socket connection to a TLS connection.\n                debug('Upgrading socket connection to TLS');\n                return tls.connect({\n                    ...omit(setServernameFromNonIpHost(opts), 'host', 'path', 'port'),\n                    socket,\n                });\n            }\n            return socket;\n        }\n        // Some other status code that's not 200... need to re-play the HTTP\n        // header \"data\" events onto the socket once the HTTP machinery is\n        // attached so that the node core `http` can parse and handle the\n        // error status code.\n        // Close the original socket, and a new \"fake\" socket is returned\n        // instead, so that the proxy doesn't get the HTTP request\n        // written to it (which may contain `Authorization` headers or other\n        // sensitive data).\n        //\n        // See: https://hackerone.com/reports/541502\n        socket.destroy();\n        const fakeSocket = new net.Socket({ writable: false });\n        fakeSocket.readable = true;\n        // Need to wait for the \"socket\" event to re-play the \"data\" events.\n        req.once('socket', (s) => {\n            debug('Replaying proxy buffer for failed request');\n            (0, assert_1.default)(s.listenerCount('data') > 0);\n            // Replay the \"buffered\" Buffer onto the fake `socket`, since at\n            // this point the HTTP module machinery has been hooked up for\n            // the user.\n            s.push(buffered);\n            s.push(null);\n        });\n        return fakeSocket;\n    }\n}\nHttpsProxyAgent.protocols = ['http', 'https'];\nexports.HttpsProxyAgent = HttpsProxyAgent;\nfunction resume(socket) {\n    socket.resume();\n}\nfunction omit(obj, ...keys) {\n    const ret = {};\n    let key;\n    for (key in obj) {\n        if (!keys.includes(key)) {\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js":
/*!*********************************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/parse-proxy-response.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseProxyResponse = void 0;\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst debug = (0, debug_1.default)('https-proxy-agent:parse-proxy-response');\nfunction parseProxyResponse(socket) {\n    return new Promise((resolve, reject) => {\n        // we need to buffer any HTTP traffic that happens with the proxy before we get\n        // the CONNECT response, so that if the response is anything other than an \"200\"\n        // response code, then we can re-play the \"data\" events on the socket once the\n        // HTTP parser is hooked up...\n        let buffersLength = 0;\n        const buffers = [];\n        function read() {\n            const b = socket.read();\n            if (b)\n                ondata(b);\n            else\n                socket.once('readable', read);\n        }\n        function cleanup() {\n            socket.removeListener('end', onend);\n            socket.removeListener('error', onerror);\n            socket.removeListener('readable', read);\n        }\n        function onend() {\n            cleanup();\n            debug('onend');\n            reject(new Error('Proxy connection ended before receiving CONNECT response'));\n        }\n        function onerror(err) {\n            cleanup();\n            debug('onerror %o', err);\n            reject(err);\n        }\n        function ondata(b) {\n            buffers.push(b);\n            buffersLength += b.length;\n            const buffered = Buffer.concat(buffers, buffersLength);\n            const endOfHeaders = buffered.indexOf('\\r\\n\\r\\n');\n            if (endOfHeaders === -1) {\n                // keep buffering\n                debug('have not received end of HTTP headers yet...');\n                read();\n                return;\n            }\n            const headerParts = buffered\n                .slice(0, endOfHeaders)\n                .toString('ascii')\n                .split('\\r\\n');\n            const firstLine = headerParts.shift();\n            if (!firstLine) {\n                socket.destroy();\n                return reject(new Error('No header received from proxy CONNECT response'));\n            }\n            const firstLineParts = firstLine.split(' ');\n            const statusCode = +firstLineParts[1];\n            const statusText = firstLineParts.slice(2).join(' ');\n            const headers = {};\n            for (const header of headerParts) {\n                if (!header)\n                    continue;\n                const firstColon = header.indexOf(':');\n                if (firstColon === -1) {\n                    socket.destroy();\n                    return reject(new Error(`Invalid header from proxy CONNECT response: \"${header}\"`));\n                }\n                const key = header.slice(0, firstColon).toLowerCase();\n                const value = header.slice(firstColon + 1).trimStart();\n                const current = headers[key];\n                if (typeof current === 'string') {\n                    headers[key] = [current, value];\n                }\n                else if (Array.isArray(current)) {\n                    current.push(value);\n                }\n                else {\n                    headers[key] = value;\n                }\n            }\n            debug('got proxy server response: %o %o', firstLine, headers);\n            cleanup();\n            resolve({\n                connect: {\n                    statusCode,\n                    statusText,\n                    headers,\n                },\n                buffered,\n            });\n        }\n        socket.on('error', onerror);\n        socket.on('end', onend);\n        read();\n    });\n}\nexports.parseProxyResponse = parseProxyResponse;\n//# sourceMappingURL=parse-proxy-response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\n");

/***/ })

};
;