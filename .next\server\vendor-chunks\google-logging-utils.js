"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/google-logging-utils";
exports.ids = ["vendor-chunks/google-logging-utils"];
exports.modules = {

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/colours.js":
/*!****************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/colours.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Colours = void 0;\n/**\n * Handles figuring out if we can use ANSI colours and handing out the escape codes.\n *\n * This is for package-internal use only, and may change at any time.\n *\n * @private\n * @internal\n */\nclass Colours {\n    /**\n     * @param stream The stream (e.g. process.stderr)\n     * @returns true if the stream should have colourization enabled\n     */\n    static isEnabled(stream) {\n        return (stream && // May happen in browsers.\n            stream.isTTY &&\n            (typeof stream.getColorDepth === 'function'\n                ? stream.getColorDepth() > 2\n                : true));\n    }\n    static refresh() {\n        Colours.enabled = Colours.isEnabled(process === null || process === void 0 ? void 0 : process.stderr);\n        if (!this.enabled) {\n            Colours.reset = '';\n            Colours.bright = '';\n            Colours.dim = '';\n            Colours.red = '';\n            Colours.green = '';\n            Colours.yellow = '';\n            Colours.blue = '';\n            Colours.magenta = '';\n            Colours.cyan = '';\n            Colours.white = '';\n            Colours.grey = '';\n        }\n        else {\n            Colours.reset = '\\u001b[0m';\n            Colours.bright = '\\u001b[1m';\n            Colours.dim = '\\u001b[2m';\n            Colours.red = '\\u001b[31m';\n            Colours.green = '\\u001b[32m';\n            Colours.yellow = '\\u001b[33m';\n            Colours.blue = '\\u001b[34m';\n            Colours.magenta = '\\u001b[35m';\n            Colours.cyan = '\\u001b[36m';\n            Colours.white = '\\u001b[37m';\n            Colours.grey = '\\u001b[90m';\n        }\n    }\n}\nexports.Colours = Colours;\nColours.enabled = false;\nColours.reset = '';\nColours.bright = '';\nColours.dim = '';\nColours.red = '';\nColours.green = '';\nColours.yellow = '';\nColours.blue = '';\nColours.magenta = '';\nColours.cyan = '';\nColours.white = '';\nColours.grey = '';\nColours.refresh();\n//# sourceMappingURL=colours.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/colours.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js":
/*!**********************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/logging-utils.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2021-2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.env = exports.DebugLogBackendBase = exports.placeholder = exports.AdhocDebugLogger = exports.LogSeverity = void 0;\nexports.getNodeBackend = getNodeBackend;\nexports.getDebugBackend = getDebugBackend;\nexports.getStructuredBackend = getStructuredBackend;\nexports.setBackend = setBackend;\nexports.log = log;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst process = __importStar(__webpack_require__(/*! process */ \"process\"));\nconst util = __importStar(__webpack_require__(/*! util */ \"util\"));\nconst colours_1 = __webpack_require__(/*! ./colours */ \"(rsc)/./node_modules/google-logging-utils/build/src/colours.js\");\n// Some functions (as noted) are based on the Node standard library, from\n// the following file:\n//\n// https://github.com/nodejs/node/blob/main/lib/internal/util/debuglog.js\n/**\n * This module defines an ad-hoc debug logger for Google Cloud Platform\n * client libraries in Node. An ad-hoc debug logger is a tool which lets\n * users use an external, unified interface (in this case, environment\n * variables) to determine what logging they want to see at runtime. This\n * isn't necessarily fed into the console, but is meant to be under the\n * control of the user. The kind of logging that will be produced by this\n * is more like \"call retry happened\", not \"events you'd want to record\n * in Cloud Logger\".\n *\n * More for Googlers implementing libraries with it:\n * go/cloud-client-logging-design\n */\n/**\n * Possible log levels. These are a subset of Cloud Observability levels.\n * https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#LogSeverity\n */\nvar LogSeverity;\n(function (LogSeverity) {\n    LogSeverity[\"DEFAULT\"] = \"DEFAULT\";\n    LogSeverity[\"DEBUG\"] = \"DEBUG\";\n    LogSeverity[\"INFO\"] = \"INFO\";\n    LogSeverity[\"WARNING\"] = \"WARNING\";\n    LogSeverity[\"ERROR\"] = \"ERROR\";\n})(LogSeverity || (exports.LogSeverity = LogSeverity = {}));\n/**\n * Our logger instance. This actually contains the meat of dealing\n * with log lines, including EventEmitter. This contains the function\n * that will be passed back to users of the package.\n */\nclass AdhocDebugLogger extends events_1.EventEmitter {\n    /**\n     * @param upstream The backend will pass a function that will be\n     *   called whenever our logger function is invoked.\n     */\n    constructor(namespace, upstream) {\n        super();\n        this.namespace = namespace;\n        this.upstream = upstream;\n        this.func = Object.assign(this.invoke.bind(this), {\n            // Also add an instance pointer back to us.\n            instance: this,\n            // And pull over the EventEmitter functionality.\n            on: (event, listener) => this.on(event, listener),\n        });\n        // Convenience methods for log levels.\n        this.func.debug = (...args) => this.invokeSeverity(LogSeverity.DEBUG, ...args);\n        this.func.info = (...args) => this.invokeSeverity(LogSeverity.INFO, ...args);\n        this.func.warn = (...args) => this.invokeSeverity(LogSeverity.WARNING, ...args);\n        this.func.error = (...args) => this.invokeSeverity(LogSeverity.ERROR, ...args);\n        this.func.sublog = (namespace) => log(namespace, this.func);\n    }\n    invoke(fields, ...args) {\n        // Push out any upstream logger first.\n        if (this.upstream) {\n            try {\n                this.upstream(fields, ...args);\n            }\n            catch (e) {\n                // Swallow exceptions to avoid interfering with other logging.\n            }\n        }\n        // Emit sink events.\n        try {\n            this.emit('log', fields, args);\n        }\n        catch (e) {\n            // Swallow exceptions to avoid interfering with other logging.\n        }\n    }\n    invokeSeverity(severity, ...args) {\n        this.invoke({ severity }, ...args);\n    }\n}\nexports.AdhocDebugLogger = AdhocDebugLogger;\n/**\n * This can be used in place of a real logger while waiting for Promises or disabling logging.\n */\nexports.placeholder = new AdhocDebugLogger('', () => { }).func;\n/**\n * The base class for debug logging backends. It's possible to use this, but the\n * same non-guarantees above still apply (unstable interface, etc).\n *\n * @private\n * @internal\n */\nclass DebugLogBackendBase {\n    constructor() {\n        var _a;\n        this.cached = new Map();\n        this.filters = [];\n        this.filtersSet = false;\n        // Look for the Node config variable for what systems to enable. We'll store\n        // these for the log method below, which will call setFilters() once.\n        let nodeFlag = (_a = process.env[exports.env.nodeEnables]) !== null && _a !== void 0 ? _a : '*';\n        if (nodeFlag === 'all') {\n            nodeFlag = '*';\n        }\n        this.filters = nodeFlag.split(',');\n    }\n    log(namespace, fields, ...args) {\n        try {\n            if (!this.filtersSet) {\n                this.setFilters();\n                this.filtersSet = true;\n            }\n            let logger = this.cached.get(namespace);\n            if (!logger) {\n                logger = this.makeLogger(namespace);\n                this.cached.set(namespace, logger);\n            }\n            logger(fields, ...args);\n        }\n        catch (e) {\n            // Silently ignore all errors; we don't want them to interfere with\n            // the user's running app.\n            // e;\n            console.error(e);\n        }\n    }\n}\nexports.DebugLogBackendBase = DebugLogBackendBase;\n// The basic backend. This one definitely works, but it's less feature-filled.\n//\n// Rather than using util.debuglog, this implements the same basic logic directly.\n// The reason for this decision is that debuglog checks the value of the\n// NODE_DEBUG environment variable before any user code runs; we therefore\n// can't pipe our own enables into it (and util.debuglog will never print unless\n// the user duplicates it into NODE_DEBUG, which isn't reasonable).\n//\nclass NodeBackend extends DebugLogBackendBase {\n    constructor() {\n        super(...arguments);\n        // Default to allowing all systems, since we gate earlier based on whether the\n        // variable is empty.\n        this.enabledRegexp = /.*/g;\n    }\n    isEnabled(namespace) {\n        return this.enabledRegexp.test(namespace);\n    }\n    makeLogger(namespace) {\n        if (!this.enabledRegexp.test(namespace)) {\n            return () => { };\n        }\n        return (fields, ...args) => {\n            var _a;\n            // TODO: `fields` needs to be turned into a string here, one way or another.\n            const nscolour = `${colours_1.Colours.green}${namespace}${colours_1.Colours.reset}`;\n            const pid = `${colours_1.Colours.yellow}${process.pid}${colours_1.Colours.reset}`;\n            let level;\n            switch (fields.severity) {\n                case LogSeverity.ERROR:\n                    level = `${colours_1.Colours.red}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.INFO:\n                    level = `${colours_1.Colours.magenta}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.WARNING:\n                    level = `${colours_1.Colours.yellow}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                default:\n                    level = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.DEFAULT;\n                    break;\n            }\n            const msg = util.formatWithOptions({ colors: colours_1.Colours.enabled }, ...args);\n            const filteredFields = Object.assign({}, fields);\n            delete filteredFields.severity;\n            const fieldsJson = Object.getOwnPropertyNames(filteredFields).length\n                ? JSON.stringify(filteredFields)\n                : '';\n            const fieldsColour = fieldsJson\n                ? `${colours_1.Colours.grey}${fieldsJson}${colours_1.Colours.reset}`\n                : '';\n            console.error('%s [%s|%s] %s%s', pid, nscolour, level, msg, fieldsJson ? ` ${fieldsColour}` : '');\n        };\n    }\n    // Regexp patterns below are from here:\n    // https://github.com/nodejs/node/blob/c0aebed4b3395bd65d54b18d1fd00f071002ac20/lib/internal/util/debuglog.js#L36\n    setFilters() {\n        const totalFilters = this.filters.join(',');\n        const regexp = totalFilters\n            .replace(/[|\\\\{}()[\\]^$+?.]/g, '\\\\$&')\n            .replace(/\\*/g, '.*')\n            .replace(/,/g, '$|^');\n        this.enabledRegexp = new RegExp(`^${regexp}$`, 'i');\n    }\n}\n/**\n * @returns A backend based on Node util.debuglog; this is the default.\n */\nfunction getNodeBackend() {\n    return new NodeBackend();\n}\nclass DebugBackend extends DebugLogBackendBase {\n    constructor(pkg) {\n        super();\n        this.debugPkg = pkg;\n    }\n    makeLogger(namespace) {\n        const debugLogger = this.debugPkg(namespace);\n        return (fields, ...args) => {\n            // TODO: `fields` needs to be turned into a string here.\n            debugLogger(args[0], ...args.slice(1));\n        };\n    }\n    setFilters() {\n        var _a;\n        const existingFilters = (_a = process.env['NODE_DEBUG']) !== null && _a !== void 0 ? _a : '';\n        process.env['NODE_DEBUG'] = `${existingFilters}${existingFilters ? ',' : ''}${this.filters.join(',')}`;\n    }\n}\n/**\n * Creates a \"debug\" package backend. The user must call require('debug') and pass\n * the resulting object to this function.\n *\n * ```\n *  setBackend(getDebugBackend(require('debug')))\n * ```\n *\n * https://www.npmjs.com/package/debug\n *\n * Note: Google does not explicitly endorse or recommend this package; it's just\n * being provided as an option.\n *\n * @returns A backend based on the npm \"debug\" package.\n */\nfunction getDebugBackend(debugPkg) {\n    return new DebugBackend(debugPkg);\n}\n/**\n * This pretty much works like the Node logger, but it outputs structured\n * logging JSON matching Google Cloud's ingestion specs. Rather than handling\n * its own output, it wraps another backend. The passed backend must be a subclass\n * of `DebugLogBackendBase` (any of the backends exposed by this package will work).\n */\nclass StructuredBackend extends DebugLogBackendBase {\n    constructor(upstream) {\n        var _a;\n        super();\n        this.upstream = (_a = upstream) !== null && _a !== void 0 ? _a : undefined;\n    }\n    makeLogger(namespace) {\n        var _a;\n        const debugLogger = (_a = this.upstream) === null || _a === void 0 ? void 0 : _a.makeLogger(namespace);\n        return (fields, ...args) => {\n            var _a;\n            const severity = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.INFO;\n            const json = Object.assign({\n                severity,\n                message: util.format(...args),\n            }, fields);\n            const jsonString = JSON.stringify(json);\n            if (debugLogger) {\n                debugLogger(fields, jsonString);\n            }\n            else {\n                console.log('%s', jsonString);\n            }\n        };\n    }\n    setFilters() {\n        var _a;\n        (_a = this.upstream) === null || _a === void 0 ? void 0 : _a.setFilters();\n    }\n}\n/**\n * Creates a \"structured logging\" backend. This pretty much works like the\n * Node logger, but it outputs structured logging JSON matching Google\n * Cloud's ingestion specs instead of plain text.\n *\n * ```\n *  setBackend(getStructuredBackend())\n * ```\n *\n * @param upstream If you want to use something besides the Node backend to\n *   write the actual log lines into, pass that here.\n * @returns A backend based on Google Cloud structured logging.\n */\nfunction getStructuredBackend(upstream) {\n    return new StructuredBackend(upstream);\n}\n/**\n * The environment variables that we standardized on, for all ad-hoc logging.\n */\nexports.env = {\n    /**\n     * Filter wildcards specific to the Node syntax, and similar to the built-in\n     * utils.debuglog() environment variable. If missing, disables logging.\n     */\n    nodeEnables: 'GOOGLE_SDK_NODE_LOGGING',\n};\n// Keep a copy of all namespaced loggers so users can reliably .on() them.\n// Note that these cached functions will need to deal with changes in the backend.\nconst loggerCache = new Map();\n// Our current global backend. This might be:\nlet cachedBackend = undefined;\n/**\n * Set the backend to use for our log output.\n * - A backend object\n * - null to disable logging\n * - undefined for \"nothing yet\", defaults to the Node backend\n *\n * @param backend Results from one of the get*Backend() functions.\n */\nfunction setBackend(backend) {\n    cachedBackend = backend;\n    loggerCache.clear();\n}\n/**\n * Creates a logging function. Multiple calls to this with the same namespace\n * will produce the same logger, with the same event emitter hooks.\n *\n * Namespaces can be a simple string (\"system\" name), or a qualified string\n * (system:subsystem), which can be used for filtering, or for \"system:*\".\n *\n * @param namespace The namespace, a descriptive text string.\n * @returns A function you can call that works similar to console.log().\n */\nfunction log(namespace, parent) {\n    // If the enable environment variable isn't set, do nothing. The user\n    // can still choose to set a backend of their choice using the manual\n    // `setBackend()`.\n    if (!cachedBackend) {\n        const enablesFlag = process.env[exports.env.nodeEnables];\n        if (!enablesFlag) {\n            return exports.placeholder;\n        }\n    }\n    // This might happen mostly if the typings are dropped in a user's code,\n    // or if they're calling from JavaScript.\n    if (!namespace) {\n        return exports.placeholder;\n    }\n    // Handle sub-loggers.\n    if (parent) {\n        namespace = `${parent.instance.namespace}:${namespace}`;\n    }\n    // Reuse loggers so things like event sinks are persistent.\n    const existing = loggerCache.get(namespace);\n    if (existing) {\n        return existing.func;\n    }\n    // Do we have a backend yet?\n    if (cachedBackend === null) {\n        // Explicitly disabled.\n        return exports.placeholder;\n    }\n    else if (cachedBackend === undefined) {\n        // One hasn't been made yet, so default to Node.\n        cachedBackend = getNodeBackend();\n    }\n    // The logger is further wrapped so we can handle the backend changing out.\n    const logger = (() => {\n        let previousBackend = undefined;\n        const newLogger = new AdhocDebugLogger(namespace, (fields, ...args) => {\n            if (previousBackend !== cachedBackend) {\n                // Did the user pass a custom backend?\n                if (cachedBackend === null) {\n                    // Explicitly disabled.\n                    return;\n                }\n                else if (cachedBackend === undefined) {\n                    // One hasn't been made yet, so default to Node.\n                    cachedBackend = getNodeBackend();\n                }\n                previousBackend = cachedBackend;\n            }\n            cachedBackend === null || cachedBackend === void 0 ? void 0 : cachedBackend.log(namespace, fields, ...args);\n        });\n        return newLogger;\n    })();\n    loggerCache.set(namespace, logger);\n    return logger.func;\n}\n//# sourceMappingURL=logging-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\n");

/***/ })

};
;