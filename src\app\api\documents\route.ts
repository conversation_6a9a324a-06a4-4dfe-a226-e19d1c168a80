import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { uploadToGoogleDrive, updateGoogleSheet } from '@/lib/google'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')

    let query = supabase
      .from('documents')
      .select(`
        *,
        user:users!documents_user_id_fkey(id, name, email),
        admin:users!documents_admin_id_fkey(id, name, email)
      `)

    // Filter based on user role
    if (session.user.role === 'user') {
      query = query.eq('user_id', session.user.id)
    }

    if (status) {
      query = query.eq('status', status)
    }

    query = query.order('created_at', { ascending: false })

    const { data: documents, error } = await query

    if (error) {
      console.error('Error fetching documents:', error)
      return NextResponse.json({ error: 'Failed to fetch documents' }, { status: 500 })
    }

    return NextResponse.json({ documents })
  } catch (error) {
    console.error('Error in GET /api/documents:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'user') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const subject = formData.get('subject') as string
    const details = formData.get('details') as string
    const urgency = formData.get('urgency') as string

    if (!file || !subject) {
      return NextResponse.json({ error: 'File and subject are required' }, { status: 400 })
    }

    // Upload file to Google Drive
    const fileBuffer = Buffer.from(await file.arrayBuffer())
    const driveResult = await uploadToGoogleDrive(
      file.name,
      fileBuffer,
      file.type
    )

    // Save document record to database
    const { data: document, error } = await supabase
      .from('documents')
      .insert({
        user_id: session.user.id,
        subject,
        details,
        urgency: urgency || 'none',
        original_filename: file.name,
        original_file_url: driveResult.webViewLink,
        original_drive_id: driveResult.fileId,
        status: 'pending'
      })
      .select()
      .single()

    if (error) {
      console.error('Error saving document:', error)
      return NextResponse.json({ error: 'Failed to save document' }, { status: 500 })
    }

    // Update Google Sheets
    try {
      await updateGoogleSheet([
        [
          new Date().toISOString(),
          session.user.name,
          session.user.email,
          subject,
          details,
          urgency,
          'pending',
          driveResult.webViewLink,
          '' // signed document link (empty for now)
        ]
      ])
    } catch (sheetError) {
      console.error('Error updating Google Sheets:', sheetError)
      // Don't fail the request if sheet update fails
    }

    return NextResponse.json({ document })
  } catch (error) {
    console.error('Error in POST /api/documents:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
