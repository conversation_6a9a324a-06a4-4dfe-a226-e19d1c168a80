/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split('.').map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, 'access_token', 'code', 'error_description', 'error_uri', 'error', 'expires_in', 'id_token', 'iss', 'response', 'session_state', 'state', 'token_type');\n}\nfunction authorizationHeaderValue(token, tokenType = 'Bearer') {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: 'openid',\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === 'claims' && typeof value === 'object') {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === 'resource' && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== 'string') {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !('kty' in k))) {\n        throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes('client_secret_post')) {\n                properties.token_endpoint_auth_method = 'client_secret_post';\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError('provide a redirect_uri or redirect_uris, not both');\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError('provide a response_type or response_types, not both');\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== 'string' || !metadata.client_id) {\n            throw new TypeError('client_id is required');\n        }\n        const properties = {\n            grant_types: [\n                'authorization_code'\n            ],\n            id_token_signed_response_alg: 'RS256',\n            authorization_signed_response_alg: 'RS256',\n            response_types: [\n                'code'\n            ],\n            token_endpoint_auth_method: 'client_secret_basic',\n            ...this.fapi1() ? {\n                grant_types: [\n                    'authorization_code',\n                    'implicit'\n                ],\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                response_types: [\n                    'code id_token'\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case 'self_signed_tls_client_auth':\n                case 'tls_client_auth':\n                    break;\n                case 'private_key_jwt':\n                    if (!jwks) {\n                        throw new TypeError('jwks is required');\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError('token_endpoint_auth_method is required');\n                default:\n                    throw new TypeError('invalid or unsupported token_endpoint_auth_method');\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport('token', this.issuer, properties);\n        [\n            'introspection',\n            'revocation'\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        assertIssuerConfiguration(this.issuer, 'authorization_endpoint');\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, '%20');\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join('\\n');\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, 'end_session_endpoint');\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === 'string';\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError('#callbackParams only accepts string urls, http.IncomingMessage or a lookalike');\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case 'GET':\n                    return pickCb(getSearchParams(input.url));\n                case 'POST':\n                    if (input.body === undefined) {\n                        throw new TypeError('incoming message body missing, include a body parser prior to this method call');\n                    }\n                    switch(typeof input.body){\n                        case 'object':\n                        case 'string':\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString('utf-8')));\n                            }\n                            if (typeof input.body === 'string') {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError('invalid IncomingMessage body object');\n                    }\n                default:\n                    throw new TypeError('invalid IncomingMessage method');\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            id_token: [\n                'id_token'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'authorization', checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'token', checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === 'string' && params.id_token.length) {\n            throw new RPError({\n                message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === 'string' && tokenset.id_token.length) {\n                throw new RPError({\n                    message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = 'A128CBC-HS256') {\n        const header = JSON.parse(base64url.decode(jwe.split('.')[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE enc received, expected %s, got: %s',\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: 'enc'\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === 'dir' ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: 'failed to decrypt JWE',\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === 'number' || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: 'missing required JWT property auth_time',\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== 'number') {\n                throw new RPError({\n                    message: 'JWT auth_time claim must be a JSON numeric value',\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === 'number' && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    'too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i',\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    'nonce mismatch, expected %s, got: %s',\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === 'authorization') {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: 'missing required property at_hash',\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: 'missing required property c_hash',\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: 'missing required property s_hash',\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: 's_hash',\n                        source: 'state'\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    'JWT issued too far in the past, now %i, iat %i',\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'at_hash',\n                    source: 'access_token'\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'c_hash',\n                    source: 'code'\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        'iss',\n        'sub',\n        'aud',\n        'exp',\n        'iat'\n    ]) {\n        const isSelfIssued = this.issuer.issuer === 'https://self-issued.me';\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    'failed to decode JWT (%s: %s)',\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWT alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                'sub_jwk'\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace('{tenantid}', payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        'unexpected iss value, expected %s, got: %s',\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== 'number') {\n                throw new RPError({\n                    message: 'JWT iat claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== 'number') {\n                throw new RPError({\n                    message: 'JWT nbf claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        'JWT not active yet, now %i, nbf %i',\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== 'number') {\n                throw new RPError({\n                    message: 'JWT exp claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        'JWT expired, now %i, exp %i',\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: 'missing required JWT property azp',\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            'aud is missing the client_id, expected %s to be included in %j',\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        'aud mismatch, expected %s, got: %s',\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === 'string') {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        'azp mismatch, got: %s',\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, 'public');\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: 'failed to use sub_jwk claim as an asymmetric JSON Web Key',\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: 'failed to match the subject with sub_jwk',\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith('HS')) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== 'none') {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: 'sig'\n            });\n        }\n        if (!keys && header.alg === 'none') {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: 'failed to validate JWT signature',\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError('refresh_token not present in TokenSet');\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: 'refresh_token',\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, 'token', skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            'sub mismatch, expected %s, got: %s',\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? 'DPoP' : accessToken instanceof TokenSet ? accessToken.token_type : 'Bearer' } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError('access_token not present in TokenSet');\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError('no access token provided');\n        } else if (typeof accessToken !== 'string') {\n            throw new TypeError('invalid access token provided');\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: 'buffer',\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers['www-authenticate'];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith('dpop ') && parseWwwAuthenticate(wwwAuthenticate).error === 'use_dpop_nonce') {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = 'GET', via = 'header', tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'userinfo_endpoint');\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== 'GET' && options.method !== 'POST') {\n            throw new TypeError('#userinfo() method can only be POST or a GET');\n        }\n        if (via === 'body' && options.method !== 'POST') {\n            throw new TypeError('can only send body on POST');\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: 'application/jwt'\n            };\n        } else {\n            options.headers = {\n                Accept: 'application/json'\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === 'body') {\n            options.headers.Authorization = undefined;\n            options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n            options.body = new URLSearchParams();\n            options.body.append('access_token', accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === 'GET') {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers['content-type'])) {\n                throw new RPError({\n                    message: 'expected application/jwt response from the userinfo_endpoint',\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: 'failed to parse userinfo JWE payload as JSON',\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, 'response', {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        'userinfo sub mismatch, expected %s, got: %s',\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? 'sha256' : len <= 384 ? 'sha384' : len <= 512 ? 'sha512' : false;\n        if (!hash) {\n            throw new Error('unsupported symmetric encryption key derivation');\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError('client_secret is required');\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const response = await authenticatedPost.call(this, 'token', {\n            form: body,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === 'use_dpop_nonce') {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'device_authorization_endpoint');\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, 'device_authorization', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'revocation_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'revocation', {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'introspection_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'introspection', {\n            form,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, 'registration_endpoint');\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: 'application/json',\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: 'json',\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: 'POST'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: 'GET',\n            url: registrationClientUri,\n            responseType: 'json',\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: 'application/json'\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || 'none', encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || 'A128CBC-HS256' } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError('requestObject must be a plain object');\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: 'oauth-authz-req+jwt'\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === 'none') {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                ''\n            ].join('.');\n        } else {\n            const symmetric = signingAlgorithm.startsWith('HS');\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: 'sig'\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: 'oauth-authz-req+jwt'\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: 'enc'\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === 'dir' ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'pushed_authorization_request_endpoint');\n        const body = {\n            ...'request' in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, 'pushed_authorization_request', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!('expires_in' in responseBody)) {\n            throw new RPError({\n                message: 'expected expires_in in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== 'number') {\n            throw new RPError({\n                message: 'invalid expires_in value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (!('request_uri' in responseBody)) {\n            throw new RPError({\n                message: 'expected request_uri in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== 'string') {\n            throw new RPError({\n                message: 'invalid request_uri value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === 'FAPI1Client';\n    }\n    fapi2() {\n        return this.constructor.name === 'FAPI2Client';\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            'iss',\n            'exp',\n            'aud'\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError('payload must be a plain object');\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === 'node:crypto') {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError('unrecognized crypto runtime');\n        }\n        if (privateKey.type !== 'private') {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError('could not determine DPoP JWS Algorithm');\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash('sha256').update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: 'dpop+jwt',\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        case 'ECDSA':\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case 'P-256':\n                        return 'ES256';\n                    case 'P-384':\n                        return 'ES384';\n                    case 'P-521':\n                        return 'ES512';\n                    default:\n                        break;\n                }\n                break;\n            }\n        case 'RSASSA-PKCS1-v1_5':\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case 'RSA-PSS':\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError('unsupported DPoP private key');\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === 'node:crypto') {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case 'ed25519':\n            case 'ed448':\n                return 'EdDSA';\n            case 'ec':\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case 'rsa':\n            case rsaPssParams && 'rsa-pss':\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError('unsupported DPoP private key');\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === 'object' && privateKeyInput.format === 'jwk' && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === 'rsa-pss') {\n                candidates = candidates.filter((value)=>value.startsWith('PS'));\n            }\n            return [\n                'PS256',\n                'PS384',\n                'PS512',\n                'RS256',\n                'RS384',\n                'RS384'\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return 'PS256';\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.key.crv){\n            case 'P-256':\n                return 'ES256';\n            case 'secp256k1':\n                return 'ES256K';\n            case 'P-384':\n                return 'ES384';\n            case 'P-512':\n                return 'ES512';\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: 'der',\n            type: 'pkcs8'\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return 'ES256';\n        }\n        if (curveOid.equals(p384)) {\n            return 'ES384';\n        }\n        if (curveOid.equals(p521)) {\n            return 'ES512';\n        }\n        if (curveOid.equals(secp256k1)) {\n            return 'ES256K';\n        }\n        throw new TypeError('unsupported DPoP private key curve');\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === 'node:crypto' && typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.format === 'jwk') {\n        return pick(privateKeyInput.key, 'kty', 'crv', 'x', 'y', 'e', 'n');\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), 'kty', 'crv', 'x', 'y', 'e', 'n');\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === 'WebCryptoAPI') {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\n\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass DeviceFlowHandle {\n  #aborted;\n  #client;\n  #clientAssertionPayload;\n  #DPoP;\n  #exchangeBody;\n  #expires_at;\n  #interval;\n  #maxAge;\n  #response;\n  constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }) {\n    ['verification_uri', 'user_code', 'device_code'].forEach((prop) => {\n      if (typeof response[prop] !== 'string' || !response[prop]) {\n        throw new RPError(\n          `expected ${prop} string to be returned by Device Authorization Response, got %j`,\n          response[prop],\n        );\n      }\n    });\n\n    if (!Number.isSafeInteger(response.expires_in)) {\n      throw new RPError(\n        'expected expires_in number to be returned by Device Authorization Response, got %j',\n        response.expires_in,\n      );\n    }\n\n    this.#expires_at = now() + response.expires_in;\n    this.#client = client;\n    this.#DPoP = DPoP;\n    this.#maxAge = maxAge;\n    this.#exchangeBody = exchangeBody;\n    this.#clientAssertionPayload = clientAssertionPayload;\n    this.#response = response;\n    this.#interval = response.interval * 1000 || 5000;\n  }\n\n  abort() {\n    this.#aborted = true;\n  }\n\n  async poll({ signal } = {}) {\n    if ((signal && signal.aborted) || this.#aborted) {\n      throw new RPError('polling aborted');\n    }\n\n    if (this.expired()) {\n      throw new RPError(\n        'the device code %j has expired and the device authorization session has concluded',\n        this.device_code,\n      );\n    }\n\n    await new Promise((resolve) => setTimeout(resolve, this.#interval));\n\n    let tokenset;\n    try {\n      tokenset = await this.#client.grant(\n        {\n          ...this.#exchangeBody,\n          grant_type: 'urn:ietf:params:oauth:grant-type:device_code',\n          device_code: this.device_code,\n        },\n        { clientAssertionPayload: this.#clientAssertionPayload, DPoP: this.#DPoP },\n      );\n    } catch (err) {\n      switch (err instanceof OPError && err.error) {\n        case 'slow_down':\n          this.#interval += 5000;\n        case 'authorization_pending':\n          return this.poll({ signal });\n        default:\n          throw err;\n      }\n    }\n\n    if ('id_token' in tokenset) {\n      await this.#client.decryptIdToken(tokenset);\n      await this.#client.validateIdToken(tokenset, undefined, 'token', this.#maxAge);\n    }\n\n    return tokenset;\n  }\n\n  get device_code() {\n    return this.#response.device_code;\n  }\n\n  get user_code() {\n    return this.#response.user_code;\n  }\n\n  get verification_uri() {\n    return this.#response.verification_uri;\n  }\n\n  get verification_uri_complete() {\n    return this.#response.verification_uri_complete;\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.#expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.#response, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { format } = __webpack_require__(/*! util */ \"util\");\n\nclass OPError extends Error {\n  constructor({ error_description, error, error_uri, session_state, state, scope }, response) {\n    super(!error_description ? error : `${error} (${error_description})`);\n\n    Object.assign(\n      this,\n      { error },\n      error_description && { error_description },\n      error_uri && { error_uri },\n      state && { state },\n      scope && { scope },\n      session_state && { session_state },\n    );\n\n    if (response) {\n      Object.defineProperty(this, 'response', {\n        value: response,\n      });\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nclass RPError extends Error {\n  constructor(...args) {\n    if (typeof args[0] === 'string') {\n      super(format(...args));\n    } else {\n      const { message, printf, response, ...rest } = args[0];\n      if (printf) {\n        super(format(...printf));\n      } else {\n        super(message);\n      }\n      Object.assign(this, rest);\n      if (response) {\n        Object.defineProperty(this, 'response', {\n          value: response,\n        });\n      }\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nmodule.exports = {\n  OPError,\n  RPError,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZXJyb3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsU0FBUyxFQUFFLG1CQUFPLENBQUMsa0JBQU07O0FBRWpDO0FBQ0EsZ0JBQWdCLGtFQUFrRTtBQUNsRiwwQ0FBMEMsT0FBTyxHQUFHLGtCQUFrQjs7QUFFdEU7QUFDQTtBQUNBLFFBQVEsT0FBTztBQUNmLDZCQUE2QixtQkFBbUI7QUFDaEQscUJBQXFCLFdBQVc7QUFDaEMsaUJBQWlCLE9BQU87QUFDeEIsaUJBQWlCLE9BQU87QUFDeEIseUJBQXlCLGVBQWU7QUFDeEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLGNBQWMscUNBQXFDO0FBQ25EO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxlcnJvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBmb3JtYXQgfSA9IHJlcXVpcmUoJ3V0aWwnKTtcblxuY2xhc3MgT1BFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IoeyBlcnJvcl9kZXNjcmlwdGlvbiwgZXJyb3IsIGVycm9yX3VyaSwgc2Vzc2lvbl9zdGF0ZSwgc3RhdGUsIHNjb3BlIH0sIHJlc3BvbnNlKSB7XG4gICAgc3VwZXIoIWVycm9yX2Rlc2NyaXB0aW9uID8gZXJyb3IgOiBgJHtlcnJvcn0gKCR7ZXJyb3JfZGVzY3JpcHRpb259KWApO1xuXG4gICAgT2JqZWN0LmFzc2lnbihcbiAgICAgIHRoaXMsXG4gICAgICB7IGVycm9yIH0sXG4gICAgICBlcnJvcl9kZXNjcmlwdGlvbiAmJiB7IGVycm9yX2Rlc2NyaXB0aW9uIH0sXG4gICAgICBlcnJvcl91cmkgJiYgeyBlcnJvcl91cmkgfSxcbiAgICAgIHN0YXRlICYmIHsgc3RhdGUgfSxcbiAgICAgIHNjb3BlICYmIHsgc2NvcGUgfSxcbiAgICAgIHNlc3Npb25fc3RhdGUgJiYgeyBzZXNzaW9uX3N0YXRlIH0sXG4gICAgKTtcblxuICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsICdyZXNwb25zZScsIHtcbiAgICAgICAgdmFsdWU6IHJlc3BvbnNlLFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgdGhpcy5uYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lO1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG59XG5cbmNsYXNzIFJQRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKC4uLmFyZ3MpIHtcbiAgICBpZiAodHlwZW9mIGFyZ3NbMF0gPT09ICdzdHJpbmcnKSB7XG4gICAgICBzdXBlcihmb3JtYXQoLi4uYXJncykpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCB7IG1lc3NhZ2UsIHByaW50ZiwgcmVzcG9uc2UsIC4uLnJlc3QgfSA9IGFyZ3NbMF07XG4gICAgICBpZiAocHJpbnRmKSB7XG4gICAgICAgIHN1cGVyKGZvcm1hdCguLi5wcmludGYpKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgfVxuICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLCByZXN0KTtcbiAgICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ3Jlc3BvbnNlJywge1xuICAgICAgICAgIHZhbHVlOiByZXNwb25zZSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgdGhpcy5uYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lO1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBPUEVycm9yLFxuICBSUEVycm9yLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n  const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n\n  if (properties[eam] && properties[eam].endsWith('_jwt') && !properties[easa] && !issuer[easavs]) {\n    throw new TypeError(\n      `${easavs} must be configured on the issuer if ${easa} is not defined on a client`,\n    );\n  }\n}\n\nfunction assertIssuerConfiguration(issuer, endpoint) {\n  if (!issuer[endpoint]) {\n    throw new TypeError(`${endpoint} must be configured on the issuer`);\n  }\n}\n\nmodule.exports = {\n  assertSigningAlgValuesSupport,\n  assertIssuerConfiguration,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9hc3NlcnQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxpQkFBaUIsU0FBUzs7QUFFMUIsaUJBQWlCLFNBQVM7QUFDMUIsa0JBQWtCLFNBQVM7QUFDM0Isb0JBQW9CLFNBQVM7O0FBRTdCO0FBQ0E7QUFDQSxTQUFTLFFBQVEsc0NBQXNDLE1BQU07QUFDN0Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSwyQkFBMkIsVUFBVTtBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcYXNzZXJ0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGFzc2VydFNpZ25pbmdBbGdWYWx1ZXNTdXBwb3J0KGVuZHBvaW50LCBpc3N1ZXIsIHByb3BlcnRpZXMpIHtcbiAgaWYgKCFpc3N1ZXJbYCR7ZW5kcG9pbnR9X2VuZHBvaW50YF0pIHJldHVybjtcblxuICBjb25zdCBlYW0gPSBgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9tZXRob2RgO1xuICBjb25zdCBlYXNhID0gYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfc2lnbmluZ19hbGdgO1xuICBjb25zdCBlYXNhdnMgPSBgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ192YWx1ZXNfc3VwcG9ydGVkYDtcblxuICBpZiAocHJvcGVydGllc1tlYW1dICYmIHByb3BlcnRpZXNbZWFtXS5lbmRzV2l0aCgnX2p3dCcpICYmICFwcm9wZXJ0aWVzW2Vhc2FdICYmICFpc3N1ZXJbZWFzYXZzXSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXG4gICAgICBgJHtlYXNhdnN9IG11c3QgYmUgY29uZmlndXJlZCBvbiB0aGUgaXNzdWVyIGlmICR7ZWFzYX0gaXMgbm90IGRlZmluZWQgb24gYSBjbGllbnRgLFxuICAgICk7XG4gIH1cbn1cblxuZnVuY3Rpb24gYXNzZXJ0SXNzdWVyQ29uZmlndXJhdGlvbihpc3N1ZXIsIGVuZHBvaW50KSB7XG4gIGlmICghaXNzdWVyW2VuZHBvaW50XSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYCR7ZW5kcG9pbnR9IG11c3QgYmUgY29uZmlndXJlZCBvbiB0aGUgaXNzdWVyYCk7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIGFzc2VydFNpZ25pbmdBbGdWYWx1ZXNTdXBwb3J0LFxuICBhc3NlcnRJc3N1ZXJDb25maWd1cmF0aW9uLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("let encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input, encoding = 'utf8') => Buffer.from(input, encoding).toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input, encoding = 'utf8') =>\n    fromBase64(Buffer.from(input, encoding).toString('base64'));\n}\n\nconst decode = (input) => Buffer.from(input, 'base64');\n\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLHFCQUFxQjtBQUNyQixxQkFBcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxiYXNlNjR1cmwuanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGVuY29kZTtcbmlmIChCdWZmZXIuaXNFbmNvZGluZygnYmFzZTY0dXJsJykpIHtcbiAgZW5jb2RlID0gKGlucHV0LCBlbmNvZGluZyA9ICd1dGY4JykgPT4gQnVmZmVyLmZyb20oaW5wdXQsIGVuY29kaW5nKS50b1N0cmluZygnYmFzZTY0dXJsJyk7XG59IGVsc2Uge1xuICBjb25zdCBmcm9tQmFzZTY0ID0gKGJhc2U2NCkgPT4gYmFzZTY0LnJlcGxhY2UoLz0vZywgJycpLnJlcGxhY2UoL1xcKy9nLCAnLScpLnJlcGxhY2UoL1xcLy9nLCAnXycpO1xuICBlbmNvZGUgPSAoaW5wdXQsIGVuY29kaW5nID0gJ3V0ZjgnKSA9PlxuICAgIGZyb21CYXNlNjQoQnVmZmVyLmZyb20oaW5wdXQsIGVuY29kaW5nKS50b1N0cmluZygnYmFzZTY0JykpO1xufVxuXG5jb25zdCBkZWNvZGUgPSAoaW5wdXQpID0+IEJ1ZmZlci5mcm9tKGlucHV0LCAnYmFzZTY0Jyk7XG5cbm1vZHVsZS5leHBvcnRzLmRlY29kZSA9IGRlY29kZTtcbm1vZHVsZS5leHBvcnRzLmVuY29kZSA9IGVuY29kZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value) => encodeURIComponent(value).replace(/%20/g, '+');\n\nasync function clientAssertion(endpoint, payload) {\n  let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n  if (!alg) {\n    assertIssuerConfiguration(\n      this.issuer,\n      `${endpoint}_endpoint_auth_signing_alg_values_supported`,\n    );\n  }\n\n  if (this[`${endpoint}_endpoint_auth_method`] === 'client_secret_jwt') {\n    if (!alg) {\n      const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n      alg =\n        Array.isArray(supported) && supported.find((signAlg) => /^HS(?:256|384|512)/.test(signAlg));\n    }\n\n    if (!alg) {\n      throw new RPError(\n        `failed to determine a JWS Algorithm to use for ${\n          this[`${endpoint}_endpoint_auth_method`]\n        } Client Assertion`,\n      );\n    }\n\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n      .setProtectedHeader({ alg })\n      .sign(this.secretForAlg(alg));\n  }\n\n  const keystore = await keystores.get(this);\n\n  if (!keystore) {\n    throw new TypeError('no client jwks provided for signing a client assertion with');\n  }\n\n  if (!alg) {\n    const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n    alg =\n      Array.isArray(supported) &&\n      supported.find((signAlg) => keystore.get({ alg: signAlg, use: 'sig' }));\n  }\n\n  if (!alg) {\n    throw new RPError(\n      `failed to determine a JWS Algorithm to use for ${\n        this[`${endpoint}_endpoint_auth_method`]\n      } Client Assertion`,\n    );\n  }\n\n  const key = keystore.get({ alg, use: 'sig' });\n  if (!key) {\n    throw new RPError(\n      `no key found in client jwks to sign a client assertion with using alg ${alg}`,\n    );\n  }\n\n  return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n    .setProtectedHeader({ alg, kid: key.jwk && key.jwk.kid })\n    .sign(await key.keyObject(alg));\n}\n\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n  const authMethod = this[`${endpoint}_endpoint_auth_method`];\n  switch (authMethod) {\n    case 'self_signed_tls_client_auth':\n    case 'tls_client_auth':\n    case 'none':\n      return { form: { client_id: this.client_id } };\n    case 'client_secret_post':\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_post client authentication method requires a client_secret',\n        );\n      }\n      return { form: { client_id: this.client_id, client_secret: this.client_secret } };\n    case 'private_key_jwt':\n    case 'client_secret_jwt': {\n      const timestamp = now();\n\n      const assertion = await clientAssertion.call(this, endpoint, {\n        iat: timestamp,\n        exp: timestamp + 60,\n        jti: random(),\n        iss: this.client_id,\n        sub: this.client_id,\n        aud: this.issuer.issuer,\n        ...clientAssertionPayload,\n      });\n\n      return {\n        form: {\n          client_id: this.client_id,\n          client_assertion: assertion,\n          client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',\n        },\n      };\n    }\n    case 'client_secret_basic': {\n      // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n      // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n      // > The client identifier is encoded using the\n      // > \"application/x-www-form-urlencoded\" encoding algorithm per\n      // > Appendix B, and the encoded value is used as the username; the client\n      // > password is encoded using the same algorithm and used as the\n      // > password.\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_basic client authentication method requires a client_secret',\n        );\n      }\n      const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n      const value = Buffer.from(encoded).toString('base64');\n      return { headers: { Authorization: `Basic ${value}` } };\n    }\n    default: {\n      throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n    }\n  }\n}\n\nfunction resolveResponseType() {\n  const { length, 0: value } = this.response_types;\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nfunction resolveRedirectUri() {\n  const { length, 0: value } = this.redirect_uris || [];\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nasync function authenticatedPost(\n  endpoint,\n  opts,\n  { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {},\n) {\n  const auth = await authFor.call(this, endpointAuthMethod, { clientAssertionPayload });\n  const requestOpts = merge(opts, auth);\n\n  const mTLS =\n    this[`${endpointAuthMethod}_endpoint_auth_method`].includes('tls_client_auth') ||\n    (endpoint === 'token' && this.tls_client_certificate_bound_access_tokens);\n\n  let targetUrl;\n  if (mTLS && this.issuer.mtls_endpoint_aliases) {\n    targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n  }\n\n  targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n\n  if ('form' in requestOpts) {\n    for (const [key, value] of Object.entries(requestOpts.form)) {\n      if (typeof value === 'undefined') {\n        delete requestOpts.form[key];\n      }\n    }\n  }\n\n  return request.call(\n    this,\n    {\n      ...requestOpts,\n      method: 'POST',\n      url: targetUrl,\n      headers: {\n        ...(endpoint !== 'revocation'\n          ? {\n              Accept: 'application/json',\n            }\n          : undefined),\n        ...requestOpts.headers,\n      },\n    },\n    { mTLS, DPoP },\n  );\n}\n\nmodule.exports = {\n  resolveResponseType,\n  resolveRedirectUri,\n  authFor,\n  authenticatedPost,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\n\nmodule.exports = {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcc2lnblxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGNvbnN0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBIVFRQX09QVElPTlMgPSBTeW1ib2woKTtcbmNvbnN0IENMT0NLX1RPTEVSQU5DRSA9IFN5bWJvbCgpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQ0xPQ0tfVE9MRVJBTkNFLFxuICBIVFRQX09QVElPTlMsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nmodule.exports = (token) => {\n  if (typeof token !== 'string' || !token) {\n    throw new TypeError('JWT must be a string');\n  }\n\n  const { 0: header, 1: payload, 2: signature, length } = token.split('.');\n\n  if (length === 5) {\n    throw new TypeError('encrypted JWTs cannot be decoded');\n  }\n\n  if (length !== 3) {\n    throw new Error('JWTs must have three components');\n  }\n\n  try {\n    return {\n      header: JSON.parse(base64url.decode(header)),\n      payload: JSON.parse(base64url.decode(payload)),\n      signature,\n    };\n  } catch (err) {\n    throw new Error('JWT is malformed');\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWNvZGVfand0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLGdGQUFhOztBQUV2QztBQUNBO0FBQ0E7QUFDQTs7QUFFQSxVQUFVLDhDQUE4Qzs7QUFFeEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcZGVjb2RlX2p3dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBiYXNlNjR1cmwgPSByZXF1aXJlKCcuL2Jhc2U2NHVybCcpO1xuXG5tb2R1bGUuZXhwb3J0cyA9ICh0b2tlbikgPT4ge1xuICBpZiAodHlwZW9mIHRva2VuICE9PSAnc3RyaW5nJyB8fCAhdG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdKV1QgbXVzdCBiZSBhIHN0cmluZycpO1xuICB9XG5cbiAgY29uc3QgeyAwOiBoZWFkZXIsIDE6IHBheWxvYWQsIDI6IHNpZ25hdHVyZSwgbGVuZ3RoIH0gPSB0b2tlbi5zcGxpdCgnLicpO1xuXG4gIGlmIChsZW5ndGggPT09IDUpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdlbmNyeXB0ZWQgSldUcyBjYW5ub3QgYmUgZGVjb2RlZCcpO1xuICB9XG5cbiAgaWYgKGxlbmd0aCAhPT0gMykge1xuICAgIHRocm93IG5ldyBFcnJvcignSldUcyBtdXN0IGhhdmUgdGhyZWUgY29tcG9uZW50cycpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICByZXR1cm4ge1xuICAgICAgaGVhZGVyOiBKU09OLnBhcnNlKGJhc2U2NHVybC5kZWNvZGUoaGVhZGVyKSksXG4gICAgICBwYXlsb2FkOiBKU09OLnBhcnNlKGJhc2U2NHVybC5kZWNvZGUocGF5bG9hZCkpLFxuICAgICAgc2lnbmF0dXJlLFxuICAgIH07XG4gIH0gY2F0Y2ggKGVycikge1xuICAgIHRocm93IG5ldyBFcnJvcignSldUIGlzIG1hbGZvcm1lZCcpO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = globalThis.structuredClone || ((obj) => JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcZGVlcF9jbG9uZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGdsb2JhbFRoaXMuc3RydWN0dXJlZENsb25lIHx8ICgob2JqKSA9PiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG9iaikpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction defaults(deep, target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (typeof target[key] === 'undefined' && typeof value !== 'undefined') {\n        target[key] = value;\n      }\n\n      if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n        defaults(true, target[key], value);\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWZhdWx0cy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLG1CQUFtQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcc2lnblxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGRlZmF1bHRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUGxhaW5PYmplY3QgPSByZXF1aXJlKCcuL2lzX3BsYWluX29iamVjdCcpO1xuXG5mdW5jdGlvbiBkZWZhdWx0cyhkZWVwLCB0YXJnZXQsIC4uLnNvdXJjZXMpIHtcbiAgZm9yIChjb25zdCBzb3VyY2Ugb2Ygc291cmNlcykge1xuICAgIGlmICghaXNQbGFpbk9iamVjdChzb3VyY2UpKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoc291cmNlKSkge1xuICAgICAgLyogaXN0YW5idWwgaWdub3JlIGlmICovXG4gICAgICBpZiAoa2V5ID09PSAnX19wcm90b19fJyB8fCBrZXkgPT09ICdjb25zdHJ1Y3RvcicpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBpZiAodHlwZW9mIHRhcmdldFtrZXldID09PSAndW5kZWZpbmVkJyAmJiB0eXBlb2YgdmFsdWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gdmFsdWU7XG4gICAgICB9XG5cbiAgICAgIGlmIChkZWVwICYmIGlzUGxhaW5PYmplY3QodGFyZ2V0W2tleV0pICYmIGlzUGxhaW5PYmplY3QodmFsdWUpKSB7XG4gICAgICAgIGRlZmF1bHRzKHRydWUsIHRhcmdldFtrZXldLCB2YWx1ZSk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBkZWZhdWx0cy5iaW5kKHVuZGVmaW5lZCwgZmFsc2UpO1xubW9kdWxlLmV4cG9ydHMuZGVlcCA9IGRlZmF1bHRzLmJpbmQodW5kZWZpbmVkLCB0cnVlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nconst random = (bytes = 32) => base64url.encode(randomBytes(bytes));\n\nmodule.exports = {\n  random,\n  state: random,\n  nonce: random,\n  codeVerifier: random,\n  codeChallenge: (codeVerifier) =>\n    base64url.encode(createHash('sha256').update(codeVerifier).digest()),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsMEJBQTBCLEVBQUUsbUJBQU8sQ0FBQyxzQkFBUTs7QUFFcEQsa0JBQWtCLG1CQUFPLENBQUMsZ0ZBQWE7O0FBRXZDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxnZW5lcmF0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgY3JlYXRlSGFzaCwgcmFuZG9tQnl0ZXMgfSA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuXG5jb25zdCBiYXNlNjR1cmwgPSByZXF1aXJlKCcuL2Jhc2U2NHVybCcpO1xuXG5jb25zdCByYW5kb20gPSAoYnl0ZXMgPSAzMikgPT4gYmFzZTY0dXJsLmVuY29kZShyYW5kb21CeXRlcyhieXRlcykpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgcmFuZG9tLFxuICBzdGF0ZTogcmFuZG9tLFxuICBub25jZTogcmFuZG9tLFxuICBjb2RlVmVyaWZpZXI6IHJhbmRvbSxcbiAgY29kZUNoYWxsZW5nZTogKGNvZGVWZXJpZmllcikgPT5cbiAgICBiYXNlNjR1cmwuZW5jb2RlKGNyZWF0ZUhhc2goJ3NoYTI1NicpLnVwZGF0ZShjb2RlVmVyaWZpZXIpLmRpZ2VzdCgpKSxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\nmodule.exports = util.types.isKeyObject || ((obj) => obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxrQkFBTTtBQUMzQixlQUFlLG1CQUFPLENBQUMsc0JBQVE7O0FBRS9CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcaXNfa2V5X29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB1dGlsID0gcmVxdWlyZSgndXRpbCcpO1xuY29uc3QgY3J5cHRvID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gdXRpbC50eXBlcy5pc0tleU9iamVjdCB8fCAoKG9iaikgPT4gb2JqICYmIG9iaiBpbnN0YW5jZW9mIGNyeXB0by5LZXlPYmplY3QpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("module.exports = (a) => !!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxpc19wbGFpbl9vYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSAoYSkgPT4gISFhICYmIGEuY29uc3RydWN0b3IgPT09IE9iamVjdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx) => {\n  if (!caches.has(ctx)) {\n    caches.set(ctx, new LRU({ max: 100 }));\n  }\n  return caches.get(ctx);\n};\n\nasync function getKeyStore(reload = false) {\n  assertIssuerConfiguration(this, 'jwks_uri');\n\n  const keystore = keystores.get(this);\n  const cache = lrus(this);\n\n  if (reload || !keystore) {\n    if (inFlight.has(this)) {\n      return inFlight.get(this);\n    }\n    cache.reset();\n    inFlight.set(\n      this,\n      (async () => {\n        const response = await request\n          .call(this, {\n            method: 'GET',\n            responseType: 'json',\n            url: this.jwks_uri,\n            headers: {\n              Accept: 'application/json, application/jwk-set+json',\n            },\n          })\n          .finally(() => {\n            inFlight.delete(this);\n          });\n        const jwks = processResponse(response);\n\n        const joseKeyStore = KeyStore.fromJWKS(jwks, { onlyPublic: true });\n        cache.set('throttle', true, 60 * 1000);\n        keystores.set(this, joseKeyStore);\n\n        return joseKeyStore;\n      })(),\n    );\n\n    return inFlight.get(this);\n  }\n\n  return keystore;\n}\n\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n  const cache = lrus(this);\n\n  const def = {\n    kid,\n    kty,\n    alg,\n    use,\n  };\n\n  const defHash = objectHash(def, {\n    algorithm: 'sha256',\n    ignoreUnknown: true,\n    unorderedArrays: true,\n    unorderedSets: true,\n    respectType: false,\n  });\n\n  // refresh keystore on every unknown key but also only upto once every minute\n  const freshJwksUri = cache.get(defHash) || cache.get('throttle');\n\n  const keystore = await getKeyStore.call(this, !freshJwksUri);\n  const keys = keystore.all(def);\n\n  delete def.use;\n  if (keys.length === 0) {\n    throw new RPError({\n      printf: [\"no valid key found in issuer's jwks_uri for key parameters %j\", def],\n      jwks: keystore,\n    });\n  }\n\n  if (!allowMulti && keys.length > 1 && !kid) {\n    throw new RPError({\n      printf: [\n        \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n        def,\n      ],\n      jwks: keystore,\n    });\n  }\n\n  cache.set(defHash, true);\n\n  return keys;\n}\n\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nconst internal = Symbol();\n\nconst keyscore = (key, { alg, use }) => {\n  let score = 0;\n\n  if (alg && key.alg) {\n    score++;\n  }\n\n  if (use && key.use) {\n    score++;\n  }\n\n  return score;\n};\n\nfunction getKtyFromAlg(alg) {\n  switch (typeof alg === 'string' && alg.slice(0, 2)) {\n    case 'RS':\n    case 'PS':\n      return 'RSA';\n    case 'ES':\n      return 'EC';\n    case 'Ed':\n      return 'OKP';\n    default:\n      return undefined;\n  }\n}\n\nfunction getAlgorithms(use, alg, kty, crv) {\n  // Ed25519, Ed448, and secp256k1 always have \"alg\"\n  // OKP always has \"use\"\n  if (alg) {\n    return new Set([alg]);\n  }\n\n  switch (kty) {\n    case 'EC': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n      }\n\n      if (use === 'sig' || use === undefined) {\n        switch (crv) {\n          case 'P-256':\n          case 'P-384':\n            algs = algs.concat([`ES${crv.slice(-3)}`]);\n            break;\n          case 'P-521':\n            algs = algs.concat(['ES512']);\n            break;\n          case 'secp256k1':\n            if (jose.cryptoRuntime === 'node:crypto') {\n              algs = algs.concat(['ES256K']);\n            }\n            break;\n        }\n      }\n\n      return new Set(algs);\n    }\n    case 'OKP': {\n      return new Set(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n    }\n    case 'RSA': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['RSA-OAEP', 'RSA-OAEP-256', 'RSA-OAEP-384', 'RSA-OAEP-512']);\n        if (jose.cryptoRuntime === 'node:crypto') {\n          algs = algs.concat(['RSA1_5']);\n        }\n      }\n\n      if (use === 'sig' || use === undefined) {\n        algs = algs.concat(['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS512']);\n      }\n\n      return new Set(algs);\n    }\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nmodule.exports = class KeyStore {\n  #keys;\n\n  constructor(i, keys) {\n    if (i !== internal) throw new Error('invalid constructor call');\n    this.#keys = keys;\n  }\n\n  toJWKS() {\n    return {\n      keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } }) => jwk),\n    };\n  }\n\n  all({ alg, kid, use } = {}) {\n    if (!use || !alg) {\n      throw new Error();\n    }\n\n    const kty = getKtyFromAlg(alg);\n\n    const search = { alg, use };\n    return this.filter((key) => {\n      let candidate = true;\n\n      if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n        candidate = false;\n      }\n\n      if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n        candidate = false;\n      }\n\n      if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n        candidate = false;\n      }\n\n      if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n        candidate = false;\n      } else if (!key.algorithms.has(alg)) {\n        candidate = false;\n      }\n\n      return candidate;\n    }).sort((first, second) => keyscore(second, search) - keyscore(first, search));\n  }\n\n  get(...args) {\n    return this.all(...args)[0];\n  }\n\n  static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n    if (\n      !isPlainObject(jwks) ||\n      !Array.isArray(jwks.keys) ||\n      jwks.keys.some((k) => !isPlainObject(k) || !('kty' in k))\n    ) {\n      throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n\n    const keys = [];\n\n    for (let jwk of jwks.keys) {\n      jwk = clone(jwk);\n      const { kty, kid, crv } = jwk;\n\n      let { alg, use } = jwk;\n\n      if (typeof kty !== 'string' || !kty) {\n        continue;\n      }\n\n      if (use !== undefined && use !== 'sig' && use !== 'enc') {\n        continue;\n      }\n\n      if (typeof alg !== 'string' && alg !== undefined) {\n        continue;\n      }\n\n      if (typeof kid !== 'string' && kid !== undefined) {\n        continue;\n      }\n\n      if (kty === 'EC' && use === 'sig') {\n        switch (crv) {\n          case 'P-256':\n            alg = 'ES256';\n            break;\n          case 'P-384':\n            alg = 'ES384';\n            break;\n          case 'P-521':\n            alg = 'ES512';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (crv === 'secp256k1') {\n        use = 'sig';\n        alg = 'ES256K';\n      }\n\n      if (kty === 'OKP') {\n        switch (crv) {\n          case 'Ed25519':\n          case 'Ed448':\n            use = 'sig';\n            alg = 'EdDSA';\n            break;\n          case 'X25519':\n          case 'X448':\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (alg && !use) {\n        switch (true) {\n          case alg.startsWith('ECDH'):\n            use = 'enc';\n            break;\n          case alg.startsWith('RSA'):\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (onlyPrivate && (jwk.kty === 'oct' || !jwk.d)) {\n        throw new Error('jwks must only contain private keys');\n      }\n\n      if (onlyPublic && (jwk.d || jwk.k)) {\n        continue;\n      }\n\n      keys.push({\n        jwk: { ...jwk, alg, use },\n        async keyObject(alg) {\n          if (this[alg]) {\n            return this[alg];\n          }\n\n          const keyObject = await jose.importJWK(this.jwk, alg);\n          this[alg] = keyObject;\n          return keyObject;\n        },\n        get algorithms() {\n          Object.defineProperty(this, 'algorithms', {\n            value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n            enumerable: true,\n            configurable: false,\n          });\n          return this.algorithms;\n        },\n      });\n    }\n\n    return new this(internal, keys);\n  }\n\n  filter(...args) {\n    return this.#keys.filter(...args);\n  }\n\n  find(...args) {\n    return this.#keys.find(...args);\n  }\n\n  every(...args) {\n    return this.#keys.every(...args);\n  }\n\n  some(...args) {\n    return this.#keys.some(...args);\n  }\n\n  map(...args) {\n    return this.#keys.map(...args);\n  }\n\n  forEach(...args) {\n    return this.#keys.forEach(...args);\n  }\n\n  reduce(...args) {\n    return this.#keys.reduce(...args);\n  }\n\n  sort(...args) {\n    return this.#keys.sort(...args);\n  }\n\n  *[Symbol.iterator]() {\n    for (const key of this.#keys) {\n      yield key;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9rZXlzdG9yZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsOERBQU07O0FBRTNCLGNBQWMsbUJBQU8sQ0FBQyxrRkFBYztBQUNwQyxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEOztBQUVBLHlCQUF5QixVQUFVO0FBQ25DOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxjQUFjO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU8sK0JBQStCO0FBQzlEO0FBQ0E7O0FBRUEsUUFBUSxnQkFBZ0IsSUFBSTtBQUM1QjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEscUJBQXFCO0FBQ3JCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxnQ0FBZ0MsMENBQTBDLElBQUk7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLGNBQWMsZ0JBQWdCOztBQUU5QixZQUFZLFdBQVc7O0FBRXZCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxrQkFBa0I7QUFDakM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxrZXlzdG9yZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBqb3NlID0gcmVxdWlyZSgnam9zZScpO1xuXG5jb25zdCBjbG9uZSA9IHJlcXVpcmUoJy4vZGVlcF9jbG9uZScpO1xuY29uc3QgaXNQbGFpbk9iamVjdCA9IHJlcXVpcmUoJy4vaXNfcGxhaW5fb2JqZWN0Jyk7XG5cbmNvbnN0IGludGVybmFsID0gU3ltYm9sKCk7XG5cbmNvbnN0IGtleXNjb3JlID0gKGtleSwgeyBhbGcsIHVzZSB9KSA9PiB7XG4gIGxldCBzY29yZSA9IDA7XG5cbiAgaWYgKGFsZyAmJiBrZXkuYWxnKSB7XG4gICAgc2NvcmUrKztcbiAgfVxuXG4gIGlmICh1c2UgJiYga2V5LnVzZSkge1xuICAgIHNjb3JlKys7XG4gIH1cblxuICByZXR1cm4gc2NvcmU7XG59O1xuXG5mdW5jdGlvbiBnZXRLdHlGcm9tQWxnKGFsZykge1xuICBzd2l0Y2ggKHR5cGVvZiBhbGcgPT09ICdzdHJpbmcnICYmIGFsZy5zbGljZSgwLCAyKSkge1xuICAgIGNhc2UgJ1JTJzpcbiAgICBjYXNlICdQUyc6XG4gICAgICByZXR1cm4gJ1JTQSc7XG4gICAgY2FzZSAnRVMnOlxuICAgICAgcmV0dXJuICdFQyc7XG4gICAgY2FzZSAnRWQnOlxuICAgICAgcmV0dXJuICdPS1AnO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG59XG5cbmZ1bmN0aW9uIGdldEFsZ29yaXRobXModXNlLCBhbGcsIGt0eSwgY3J2KSB7XG4gIC8vIEVkMjU1MTksIEVkNDQ4LCBhbmQgc2VjcDI1NmsxIGFsd2F5cyBoYXZlIFwiYWxnXCJcbiAgLy8gT0tQIGFsd2F5cyBoYXMgXCJ1c2VcIlxuICBpZiAoYWxnKSB7XG4gICAgcmV0dXJuIG5ldyBTZXQoW2FsZ10pO1xuICB9XG5cbiAgc3dpdGNoIChrdHkpIHtcbiAgICBjYXNlICdFQyc6IHtcbiAgICAgIGxldCBhbGdzID0gW107XG5cbiAgICAgIGlmICh1c2UgPT09ICdlbmMnIHx8IHVzZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGFsZ3MgPSBhbGdzLmNvbmNhdChbJ0VDREgtRVMnLCAnRUNESC1FUytBMTI4S1cnLCAnRUNESC1FUytBMTkyS1cnLCAnRUNESC1FUytBMjU2S1cnXSk7XG4gICAgICB9XG5cbiAgICAgIGlmICh1c2UgPT09ICdzaWcnIHx8IHVzZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHN3aXRjaCAoY3J2KSB7XG4gICAgICAgICAgY2FzZSAnUC0yNTYnOlxuICAgICAgICAgIGNhc2UgJ1AtMzg0JzpcbiAgICAgICAgICAgIGFsZ3MgPSBhbGdzLmNvbmNhdChbYEVTJHtjcnYuc2xpY2UoLTMpfWBdKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ1AtNTIxJzpcbiAgICAgICAgICAgIGFsZ3MgPSBhbGdzLmNvbmNhdChbJ0VTNTEyJ10pO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAnc2VjcDI1NmsxJzpcbiAgICAgICAgICAgIGlmIChqb3NlLmNyeXB0b1J1bnRpbWUgPT09ICdub2RlOmNyeXB0bycpIHtcbiAgICAgICAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnRVMyNTZLJ10pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIG5ldyBTZXQoYWxncyk7XG4gICAgfVxuICAgIGNhc2UgJ09LUCc6IHtcbiAgICAgIHJldHVybiBuZXcgU2V0KFsnRUNESC1FUycsICdFQ0RILUVTK0ExMjhLVycsICdFQ0RILUVTK0ExOTJLVycsICdFQ0RILUVTK0EyNTZLVyddKTtcbiAgICB9XG4gICAgY2FzZSAnUlNBJzoge1xuICAgICAgbGV0IGFsZ3MgPSBbXTtcblxuICAgICAgaWYgKHVzZSA9PT0gJ2VuYycgfHwgdXNlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnUlNBLU9BRVAnLCAnUlNBLU9BRVAtMjU2JywgJ1JTQS1PQUVQLTM4NCcsICdSU0EtT0FFUC01MTInXSk7XG4gICAgICAgIGlmIChqb3NlLmNyeXB0b1J1bnRpbWUgPT09ICdub2RlOmNyeXB0bycpIHtcbiAgICAgICAgICBhbGdzID0gYWxncy5jb25jYXQoWydSU0ExXzUnXSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKHVzZSA9PT0gJ3NpZycgfHwgdXNlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgYWxncyA9IGFsZ3MuY29uY2F0KFsnUFMyNTYnLCAnUFMzODQnLCAnUFM1MTInLCAnUlMyNTYnLCAnUlMzODQnLCAnUlM1MTInXSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBuZXcgU2V0KGFsZ3MpO1xuICAgIH1cbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgbmV3IEVycm9yKCd1bnJlYWNoYWJsZScpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gY2xhc3MgS2V5U3RvcmUge1xuICAja2V5cztcblxuICBjb25zdHJ1Y3RvcihpLCBrZXlzKSB7XG4gICAgaWYgKGkgIT09IGludGVybmFsKSB0aHJvdyBuZXcgRXJyb3IoJ2ludmFsaWQgY29uc3RydWN0b3IgY2FsbCcpO1xuICAgIHRoaXMuI2tleXMgPSBrZXlzO1xuICB9XG5cbiAgdG9KV0tTKCkge1xuICAgIHJldHVybiB7XG4gICAgICBrZXlzOiB0aGlzLm1hcCgoeyBqd2s6IHsgZCwgcCwgcSwgZHAsIGRxLCBxaSwgLi4uandrIH0gfSkgPT4gandrKSxcbiAgICB9O1xuICB9XG5cbiAgYWxsKHsgYWxnLCBraWQsIHVzZSB9ID0ge30pIHtcbiAgICBpZiAoIXVzZSB8fCAhYWxnKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoKTtcbiAgICB9XG5cbiAgICBjb25zdCBrdHkgPSBnZXRLdHlGcm9tQWxnKGFsZyk7XG5cbiAgICBjb25zdCBzZWFyY2ggPSB7IGFsZywgdXNlIH07XG4gICAgcmV0dXJuIHRoaXMuZmlsdGVyKChrZXkpID0+IHtcbiAgICAgIGxldCBjYW5kaWRhdGUgPSB0cnVlO1xuXG4gICAgICBpZiAoY2FuZGlkYXRlICYmIGt0eSAhPT0gdW5kZWZpbmVkICYmIGtleS5qd2sua3R5ICE9PSBrdHkpIHtcbiAgICAgICAgY2FuZGlkYXRlID0gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGlmIChjYW5kaWRhdGUgJiYga2lkICE9PSB1bmRlZmluZWQgJiYga2V5Lmp3ay5raWQgIT09IGtpZCkge1xuICAgICAgICBjYW5kaWRhdGUgPSBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGNhbmRpZGF0ZSAmJiB1c2UgIT09IHVuZGVmaW5lZCAmJiBrZXkuandrLnVzZSAhPT0gdW5kZWZpbmVkICYmIGtleS5qd2sudXNlICE9PSB1c2UpIHtcbiAgICAgICAgY2FuZGlkYXRlID0gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGlmIChjYW5kaWRhdGUgJiYga2V5Lmp3ay5hbGcgJiYga2V5Lmp3ay5hbGcgIT09IGFsZykge1xuICAgICAgICBjYW5kaWRhdGUgPSBmYWxzZTtcbiAgICAgIH0gZWxzZSBpZiAoIWtleS5hbGdvcml0aG1zLmhhcyhhbGcpKSB7XG4gICAgICAgIGNhbmRpZGF0ZSA9IGZhbHNlO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gY2FuZGlkYXRlO1xuICAgIH0pLnNvcnQoKGZpcnN0LCBzZWNvbmQpID0+IGtleXNjb3JlKHNlY29uZCwgc2VhcmNoKSAtIGtleXNjb3JlKGZpcnN0LCBzZWFyY2gpKTtcbiAgfVxuXG4gIGdldCguLi5hcmdzKSB7XG4gICAgcmV0dXJuIHRoaXMuYWxsKC4uLmFyZ3MpWzBdO1xuICB9XG5cbiAgc3RhdGljIGFzeW5jIGZyb21KV0tTKGp3a3MsIHsgb25seVB1YmxpYyA9IGZhbHNlLCBvbmx5UHJpdmF0ZSA9IGZhbHNlIH0gPSB7fSkge1xuICAgIGlmIChcbiAgICAgICFpc1BsYWluT2JqZWN0KGp3a3MpIHx8XG4gICAgICAhQXJyYXkuaXNBcnJheShqd2tzLmtleXMpIHx8XG4gICAgICBqd2tzLmtleXMuc29tZSgoaykgPT4gIWlzUGxhaW5PYmplY3QoaykgfHwgISgna3R5JyBpbiBrKSlcbiAgICApIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2p3a3MgbXVzdCBiZSBhIEpTT04gV2ViIEtleSBTZXQgZm9ybWF0dGVkIG9iamVjdCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGtleXMgPSBbXTtcblxuICAgIGZvciAobGV0IGp3ayBvZiBqd2tzLmtleXMpIHtcbiAgICAgIGp3ayA9IGNsb25lKGp3ayk7XG4gICAgICBjb25zdCB7IGt0eSwga2lkLCBjcnYgfSA9IGp3aztcblxuICAgICAgbGV0IHsgYWxnLCB1c2UgfSA9IGp3aztcblxuICAgICAgaWYgKHR5cGVvZiBrdHkgIT09ICdzdHJpbmcnIHx8ICFrdHkpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGlmICh1c2UgIT09IHVuZGVmaW5lZCAmJiB1c2UgIT09ICdzaWcnICYmIHVzZSAhPT0gJ2VuYycpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgYWxnICE9PSAnc3RyaW5nJyAmJiBhbGcgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiBraWQgIT09ICdzdHJpbmcnICYmIGtpZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuXG4gICAgICBpZiAoa3R5ID09PSAnRUMnICYmIHVzZSA9PT0gJ3NpZycpIHtcbiAgICAgICAgc3dpdGNoIChjcnYpIHtcbiAgICAgICAgICBjYXNlICdQLTI1Nic6XG4gICAgICAgICAgICBhbGcgPSAnRVMyNTYnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAnUC0zODQnOlxuICAgICAgICAgICAgYWxnID0gJ0VTMzg0JztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ1AtNTIxJzpcbiAgICAgICAgICAgIGFsZyA9ICdFUzUxMic7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKGNydiA9PT0gJ3NlY3AyNTZrMScpIHtcbiAgICAgICAgdXNlID0gJ3NpZyc7XG4gICAgICAgIGFsZyA9ICdFUzI1NksnO1xuICAgICAgfVxuXG4gICAgICBpZiAoa3R5ID09PSAnT0tQJykge1xuICAgICAgICBzd2l0Y2ggKGNydikge1xuICAgICAgICAgIGNhc2UgJ0VkMjU1MTknOlxuICAgICAgICAgIGNhc2UgJ0VkNDQ4JzpcbiAgICAgICAgICAgIHVzZSA9ICdzaWcnO1xuICAgICAgICAgICAgYWxnID0gJ0VkRFNBJztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ1gyNTUxOSc6XG4gICAgICAgICAgY2FzZSAnWDQ0OCc6XG4gICAgICAgICAgICB1c2UgPSAnZW5jJztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoYWxnICYmICF1c2UpIHtcbiAgICAgICAgc3dpdGNoICh0cnVlKSB7XG4gICAgICAgICAgY2FzZSBhbGcuc3RhcnRzV2l0aCgnRUNESCcpOlxuICAgICAgICAgICAgdXNlID0gJ2VuYyc7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIGFsZy5zdGFydHNXaXRoKCdSU0EnKTpcbiAgICAgICAgICAgIHVzZSA9ICdlbmMnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChvbmx5UHJpdmF0ZSAmJiAoandrLmt0eSA9PT0gJ29jdCcgfHwgIWp3ay5kKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2p3a3MgbXVzdCBvbmx5IGNvbnRhaW4gcHJpdmF0ZSBrZXlzJyk7XG4gICAgICB9XG5cbiAgICAgIGlmIChvbmx5UHVibGljICYmIChqd2suZCB8fCBqd2suaykpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGtleXMucHVzaCh7XG4gICAgICAgIGp3azogeyAuLi5qd2ssIGFsZywgdXNlIH0sXG4gICAgICAgIGFzeW5jIGtleU9iamVjdChhbGcpIHtcbiAgICAgICAgICBpZiAodGhpc1thbGddKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpc1thbGddO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IGtleU9iamVjdCA9IGF3YWl0IGpvc2UuaW1wb3J0SldLKHRoaXMuandrLCBhbGcpO1xuICAgICAgICAgIHRoaXNbYWxnXSA9IGtleU9iamVjdDtcbiAgICAgICAgICByZXR1cm4ga2V5T2JqZWN0O1xuICAgICAgICB9LFxuICAgICAgICBnZXQgYWxnb3JpdGhtcygpIHtcbiAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ2FsZ29yaXRobXMnLCB7XG4gICAgICAgICAgICB2YWx1ZTogZ2V0QWxnb3JpdGhtcyh0aGlzLmp3ay51c2UsIHRoaXMuandrLmFsZywgdGhpcy5qd2sua3R5LCB0aGlzLmp3ay5jcnYpLFxuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogZmFsc2UsXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuIHRoaXMuYWxnb3JpdGhtcztcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHJldHVybiBuZXcgdGhpcyhpbnRlcm5hbCwga2V5cyk7XG4gIH1cblxuICBmaWx0ZXIoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLmZpbHRlciguLi5hcmdzKTtcbiAgfVxuXG4gIGZpbmQoLi4uYXJncykge1xuICAgIHJldHVybiB0aGlzLiNrZXlzLmZpbmQoLi4uYXJncyk7XG4gIH1cblxuICBldmVyeSguLi5hcmdzKSB7XG4gICAgcmV0dXJuIHRoaXMuI2tleXMuZXZlcnkoLi4uYXJncyk7XG4gIH1cblxuICBzb21lKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5zb21lKC4uLmFyZ3MpO1xuICB9XG5cbiAgbWFwKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5tYXAoLi4uYXJncyk7XG4gIH1cblxuICBmb3JFYWNoKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5mb3JFYWNoKC4uLmFyZ3MpO1xuICB9XG5cbiAgcmVkdWNlKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5yZWR1Y2UoLi4uYXJncyk7XG4gIH1cblxuICBzb3J0KC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gdGhpcy4ja2V5cy5zb3J0KC4uLmFyZ3MpO1xuICB9XG5cbiAgKltTeW1ib2wuaXRlcmF0b3JdKCkge1xuICAgIGZvciAoY29uc3Qga2V5IG9mIHRoaXMuI2tleXMpIHtcbiAgICAgIHlpZWxkIGtleTtcbiAgICB9XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction merge(target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (isPlainObject(target[key]) && isPlainObject(value)) {\n        target[key] = merge(target[key], value);\n      } else if (typeof value !== 'undefined') {\n        target[key] = value;\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcbWVyZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNQbGFpbk9iamVjdCA9IHJlcXVpcmUoJy4vaXNfcGxhaW5fb2JqZWN0Jyk7XG5cbmZ1bmN0aW9uIG1lcmdlKHRhcmdldCwgLi4uc291cmNlcykge1xuICBmb3IgKGNvbnN0IHNvdXJjZSBvZiBzb3VyY2VzKSB7XG4gICAgaWYgKCFpc1BsYWluT2JqZWN0KHNvdXJjZSkpIHtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhzb3VyY2UpKSB7XG4gICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgaWYgKi9cbiAgICAgIGlmIChrZXkgPT09ICdfX3Byb3RvX18nIHx8IGtleSA9PT0gJ2NvbnN0cnVjdG9yJykge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGlmIChpc1BsYWluT2JqZWN0KHRhcmdldFtrZXldKSAmJiBpc1BsYWluT2JqZWN0KHZhbHVlKSkge1xuICAgICAgICB0YXJnZXRba2V5XSA9IG1lcmdlKHRhcmdldFtrZXldLCB2YWx1ZSk7XG4gICAgICB9IGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IG1lcmdlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("module.exports = function pick(object, ...paths) {\n  const obj = {};\n  for (const path of paths) {\n    if (object[path] !== undefined) {\n      obj[path] = object[path];\n    }\n  }\n  return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcc2lnblxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHBpY2suanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBwaWNrKG9iamVjdCwgLi4ucGF0aHMpIHtcbiAgY29uc3Qgb2JqID0ge307XG4gIGZvciAoY29uc3QgcGF0aCBvZiBwYXRocykge1xuICAgIGlmIChvYmplY3RbcGF0aF0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgb2JqW3BhdGhdID0gb2JqZWN0W3BhdGhdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gb2JqO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\n\nconst throwAuthenticateErrors = (response) => {\n  const params = parseWwwAuthenticate(response.headers['www-authenticate']);\n\n  if (params.error) {\n    throw new OPError(params, response);\n  }\n};\n\nconst isStandardBodyError = (response) => {\n  let result = false;\n  try {\n    let jsonbody;\n    if (typeof response.body !== 'object' || Buffer.isBuffer(response.body)) {\n      jsonbody = JSON.parse(response.body);\n    } else {\n      jsonbody = response.body;\n    }\n    result = typeof jsonbody.error === 'string' && jsonbody.error.length;\n    if (result) Object.defineProperty(response, 'body', { value: jsonbody, configurable: true });\n  } catch (err) {}\n\n  return result;\n};\n\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n  if (response.statusCode !== statusCode) {\n    if (bearer) {\n      throwAuthenticateErrors(response);\n    }\n\n    if (isStandardBodyError(response)) {\n      throw new OPError(response.body, response);\n    }\n\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s, got: %i %s',\n          statusCode,\n          STATUS_CODES[statusCode],\n          response.statusCode,\n          STATUS_CODES[response.statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  if (body && !response.body) {\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s with body but no body was returned',\n          statusCode,\n          STATUS_CODES[statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  return response.body;\n}\n\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\n\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\n\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\n\nconst allowed = [\n  'agent',\n  'ca',\n  'cert',\n  'crl',\n  'headers',\n  'key',\n  'lookup',\n  'passphrase',\n  'pfx',\n  'timeout',\n];\n\nconst setDefaults = (props, options) => {\n  DEFAULT_HTTP_OPTIONS = defaultsDeep(\n    {},\n    props.length ? pick(options, ...props) : options,\n    DEFAULT_HTTP_OPTIONS,\n  );\n};\n\nsetDefaults([], {\n  headers: {\n    'User-Agent': `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n    'Accept-Encoding': 'identity',\n  },\n  timeout: 3500,\n});\n\nfunction send(req, body, contentType) {\n  if (contentType) {\n    req.removeHeader('content-type');\n    req.setHeader('content-type', contentType);\n  }\n  if (body) {\n    req.removeHeader('content-length');\n    req.setHeader('content-length', Buffer.byteLength(body));\n    req.write(body);\n  }\n  req.end();\n}\n\nconst nonces = new LRU({ max: 100 });\n\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n  let url;\n  try {\n    url = new URL(options.url);\n    delete options.url;\n    assert(/^(https?:)$/.test(url.protocol));\n  } catch (err) {\n    throw new TypeError('only valid absolute URLs can be requested');\n  }\n  const optsFn = this[HTTP_OPTIONS];\n  let opts = options;\n\n  const nonceKey = `${url.origin}${url.pathname}`;\n  if (DPoP && 'dpopProof' in this) {\n    opts.headers = opts.headers || {};\n    opts.headers.DPoP = await this.dpopProof(\n      {\n        htu: `${url.origin}${url.pathname}`,\n        htm: options.method || 'GET',\n        nonce: nonces.get(nonceKey),\n      },\n      DPoP,\n      accessToken,\n    );\n  }\n\n  let userOptions;\n  if (optsFn) {\n    userOptions = pick(\n      optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)),\n      ...allowed,\n    );\n  }\n  opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n\n  if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n    throw new TypeError('mutual-TLS certificate and key not set');\n  }\n\n  if (opts.searchParams) {\n    for (const [key, value] of Object.entries(opts.searchParams)) {\n      url.searchParams.delete(key);\n      url.searchParams.set(key, value);\n    }\n  }\n\n  let responseType;\n  let form;\n  let json;\n  let body;\n  ({ form, responseType, json, body, ...opts } = opts);\n\n  for (const [key, value] of Object.entries(opts.headers || {})) {\n    if (value === undefined) {\n      delete opts.headers[key];\n    }\n  }\n\n  let response;\n  const req = (url.protocol === 'https:' ? https.request : http.request)(url.href, opts);\n  return (async () => {\n    if (json) {\n      send(req, JSON.stringify(json), 'application/json');\n    } else if (form) {\n      send(req, querystring.stringify(form), 'application/x-www-form-urlencoded');\n    } else if (body) {\n      send(req, body);\n    } else {\n      send(req);\n    }\n\n    [response] = await Promise.race([once(req, 'response'), once(req, 'timeout')]);\n\n    // timeout reached\n    if (!response) {\n      req.destroy();\n      throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n    }\n\n    const parts = [];\n\n    for await (const part of response) {\n      parts.push(part);\n    }\n\n    if (parts.length) {\n      switch (responseType) {\n        case 'json': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              let value = Buffer.concat(parts);\n              try {\n                value = JSON.parse(value);\n              } catch (err) {\n                Object.defineProperty(err, 'response', { value: response });\n                throw err;\n              } finally {\n                Object.defineProperty(response, 'body', { value, configurable: true });\n              }\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        case undefined:\n        case 'buffer': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              const value = Buffer.concat(parts);\n              Object.defineProperty(response, 'body', { value, configurable: true });\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        default:\n          throw new TypeError('unsupported responseType request option');\n      }\n    }\n\n    return response;\n  })()\n    .catch((err) => {\n      if (response) Object.defineProperty(err, 'response', { value: response });\n      throw err;\n    })\n    .finally(() => {\n      const dpopNonce = response && response.headers['dpop-nonce'];\n      if (dpopNonce && NQCHAR.test(dpopNonce)) {\n        nonces.set(nonceKey, dpopNonce);\n      }\n    });\n};\n\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9yZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsbUJBQU8sQ0FBQyxzQkFBUTtBQUMvQixvQkFBb0IsbUJBQU8sQ0FBQyxnQ0FBYTtBQUN6QyxhQUFhLG1CQUFPLENBQUMsa0JBQU07QUFDM0IsY0FBYyxtQkFBTyxDQUFDLG9CQUFPO0FBQzdCLFFBQVEsT0FBTyxFQUFFLG1CQUFPLENBQUMsc0JBQVE7QUFDakMsUUFBUSxNQUFNLEVBQUUsbUJBQU8sQ0FBQyxnQkFBSzs7QUFFN0IsWUFBWSxtQkFBTyxDQUFDLDBEQUFXOztBQUUvQixZQUFZLG1CQUFPLENBQUMsMkVBQW9CO0FBQ3hDLFFBQVEsVUFBVSxFQUFFLG1CQUFPLENBQUMsbUVBQVc7O0FBRXZDLGFBQWEsbUJBQU8sQ0FBQyxzRUFBUTtBQUM3QixRQUFRLHFCQUFxQixFQUFFLG1CQUFPLENBQUMsOEVBQVk7QUFDbkQsUUFBUSxlQUFlLEVBQUUsbUJBQU8sQ0FBQywwRUFBVTs7QUFFM0M7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EscUJBQXFCLFNBQVMsR0FBRyxhQUFhLEdBQUcsYUFBYTtBQUM5RDtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QixVQUFVOztBQUVuQyxtREFBbUQsa0NBQWtDLElBQUk7QUFDekY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0IsV0FBVyxFQUFFLGFBQWE7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsV0FBVyxFQUFFLGFBQWE7QUFDMUM7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCOztBQUV4QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSywwQ0FBMEM7O0FBRS9DLDhEQUE4RDtBQUM5RDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsNERBQTRELGFBQWE7QUFDekU7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIseURBQXlELGlCQUFpQjtBQUMxRTtBQUNBLGdCQUFnQjtBQUNoQiwwREFBMEQsMkJBQTJCO0FBQ3JGO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0QsMkJBQTJCO0FBQ25GO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEdBQUc7QUFDSDtBQUNBLDZEQUE2RCxpQkFBaUI7QUFDOUU7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQSwwQkFBMEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxyZXF1ZXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGFzc2VydCA9IHJlcXVpcmUoJ2Fzc2VydCcpO1xuY29uc3QgcXVlcnlzdHJpbmcgPSByZXF1aXJlKCdxdWVyeXN0cmluZycpO1xuY29uc3QgaHR0cCA9IHJlcXVpcmUoJ2h0dHAnKTtcbmNvbnN0IGh0dHBzID0gcmVxdWlyZSgnaHR0cHMnKTtcbmNvbnN0IHsgb25jZSB9ID0gcmVxdWlyZSgnZXZlbnRzJyk7XG5jb25zdCB7IFVSTCB9ID0gcmVxdWlyZSgndXJsJyk7XG5cbmNvbnN0IExSVSA9IHJlcXVpcmUoJ2xydS1jYWNoZScpO1xuXG5jb25zdCBwa2cgPSByZXF1aXJlKCcuLi8uLi9wYWNrYWdlLmpzb24nKTtcbmNvbnN0IHsgUlBFcnJvciB9ID0gcmVxdWlyZSgnLi4vZXJyb3JzJyk7XG5cbmNvbnN0IHBpY2sgPSByZXF1aXJlKCcuL3BpY2snKTtcbmNvbnN0IHsgZGVlcDogZGVmYXVsdHNEZWVwIH0gPSByZXF1aXJlKCcuL2RlZmF1bHRzJyk7XG5jb25zdCB7IEhUVFBfT1BUSU9OUyB9ID0gcmVxdWlyZSgnLi9jb25zdHMnKTtcblxubGV0IERFRkFVTFRfSFRUUF9PUFRJT05TO1xuY29uc3QgTlFDSEFSID0gL15bXFx4MjFcXHgyMy1cXHg1QlxceDVELVxceDdFXSskLztcblxuY29uc3QgYWxsb3dlZCA9IFtcbiAgJ2FnZW50JyxcbiAgJ2NhJyxcbiAgJ2NlcnQnLFxuICAnY3JsJyxcbiAgJ2hlYWRlcnMnLFxuICAna2V5JyxcbiAgJ2xvb2t1cCcsXG4gICdwYXNzcGhyYXNlJyxcbiAgJ3BmeCcsXG4gICd0aW1lb3V0Jyxcbl07XG5cbmNvbnN0IHNldERlZmF1bHRzID0gKHByb3BzLCBvcHRpb25zKSA9PiB7XG4gIERFRkFVTFRfSFRUUF9PUFRJT05TID0gZGVmYXVsdHNEZWVwKFxuICAgIHt9LFxuICAgIHByb3BzLmxlbmd0aCA/IHBpY2sob3B0aW9ucywgLi4ucHJvcHMpIDogb3B0aW9ucyxcbiAgICBERUZBVUxUX0hUVFBfT1BUSU9OUyxcbiAgKTtcbn07XG5cbnNldERlZmF1bHRzKFtdLCB7XG4gIGhlYWRlcnM6IHtcbiAgICAnVXNlci1BZ2VudCc6IGAke3BrZy5uYW1lfS8ke3BrZy52ZXJzaW9ufSAoJHtwa2cuaG9tZXBhZ2V9KWAsXG4gICAgJ0FjY2VwdC1FbmNvZGluZyc6ICdpZGVudGl0eScsXG4gIH0sXG4gIHRpbWVvdXQ6IDM1MDAsXG59KTtcblxuZnVuY3Rpb24gc2VuZChyZXEsIGJvZHksIGNvbnRlbnRUeXBlKSB7XG4gIGlmIChjb250ZW50VHlwZSkge1xuICAgIHJlcS5yZW1vdmVIZWFkZXIoJ2NvbnRlbnQtdHlwZScpO1xuICAgIHJlcS5zZXRIZWFkZXIoJ2NvbnRlbnQtdHlwZScsIGNvbnRlbnRUeXBlKTtcbiAgfVxuICBpZiAoYm9keSkge1xuICAgIHJlcS5yZW1vdmVIZWFkZXIoJ2NvbnRlbnQtbGVuZ3RoJyk7XG4gICAgcmVxLnNldEhlYWRlcignY29udGVudC1sZW5ndGgnLCBCdWZmZXIuYnl0ZUxlbmd0aChib2R5KSk7XG4gICAgcmVxLndyaXRlKGJvZHkpO1xuICB9XG4gIHJlcS5lbmQoKTtcbn1cblxuY29uc3Qgbm9uY2VzID0gbmV3IExSVSh7IG1heDogMTAwIH0pO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGFzeW5jIGZ1bmN0aW9uIHJlcXVlc3Qob3B0aW9ucywgeyBhY2Nlc3NUb2tlbiwgbVRMUyA9IGZhbHNlLCBEUG9QIH0gPSB7fSkge1xuICBsZXQgdXJsO1xuICB0cnkge1xuICAgIHVybCA9IG5ldyBVUkwob3B0aW9ucy51cmwpO1xuICAgIGRlbGV0ZSBvcHRpb25zLnVybDtcbiAgICBhc3NlcnQoL14oaHR0cHM/OikkLy50ZXN0KHVybC5wcm90b2NvbCkpO1xuICB9IGNhdGNoIChlcnIpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdvbmx5IHZhbGlkIGFic29sdXRlIFVSTHMgY2FuIGJlIHJlcXVlc3RlZCcpO1xuICB9XG4gIGNvbnN0IG9wdHNGbiA9IHRoaXNbSFRUUF9PUFRJT05TXTtcbiAgbGV0IG9wdHMgPSBvcHRpb25zO1xuXG4gIGNvbnN0IG5vbmNlS2V5ID0gYCR7dXJsLm9yaWdpbn0ke3VybC5wYXRobmFtZX1gO1xuICBpZiAoRFBvUCAmJiAnZHBvcFByb29mJyBpbiB0aGlzKSB7XG4gICAgb3B0cy5oZWFkZXJzID0gb3B0cy5oZWFkZXJzIHx8IHt9O1xuICAgIG9wdHMuaGVhZGVycy5EUG9QID0gYXdhaXQgdGhpcy5kcG9wUHJvb2YoXG4gICAgICB7XG4gICAgICAgIGh0dTogYCR7dXJsLm9yaWdpbn0ke3VybC5wYXRobmFtZX1gLFxuICAgICAgICBodG06IG9wdGlvbnMubWV0aG9kIHx8ICdHRVQnLFxuICAgICAgICBub25jZTogbm9uY2VzLmdldChub25jZUtleSksXG4gICAgICB9LFxuICAgICAgRFBvUCxcbiAgICAgIGFjY2Vzc1Rva2VuLFxuICAgICk7XG4gIH1cblxuICBsZXQgdXNlck9wdGlvbnM7XG4gIGlmIChvcHRzRm4pIHtcbiAgICB1c2VyT3B0aW9ucyA9IHBpY2soXG4gICAgICBvcHRzRm4uY2FsbCh0aGlzLCB1cmwsIGRlZmF1bHRzRGVlcCh7fSwgb3B0cywgREVGQVVMVF9IVFRQX09QVElPTlMpKSxcbiAgICAgIC4uLmFsbG93ZWQsXG4gICAgKTtcbiAgfVxuICBvcHRzID0gZGVmYXVsdHNEZWVwKHt9LCB1c2VyT3B0aW9ucywgb3B0cywgREVGQVVMVF9IVFRQX09QVElPTlMpO1xuXG4gIGlmIChtVExTICYmICFvcHRzLnBmeCAmJiAhKG9wdHMua2V5ICYmIG9wdHMuY2VydCkpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdtdXR1YWwtVExTIGNlcnRpZmljYXRlIGFuZCBrZXkgbm90IHNldCcpO1xuICB9XG5cbiAgaWYgKG9wdHMuc2VhcmNoUGFyYW1zKSB7XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMob3B0cy5zZWFyY2hQYXJhbXMpKSB7XG4gICAgICB1cmwuc2VhcmNoUGFyYW1zLmRlbGV0ZShrZXkpO1xuICAgICAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoa2V5LCB2YWx1ZSk7XG4gICAgfVxuICB9XG5cbiAgbGV0IHJlc3BvbnNlVHlwZTtcbiAgbGV0IGZvcm07XG4gIGxldCBqc29uO1xuICBsZXQgYm9keTtcbiAgKHsgZm9ybSwgcmVzcG9uc2VUeXBlLCBqc29uLCBib2R5LCAuLi5vcHRzIH0gPSBvcHRzKTtcblxuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhvcHRzLmhlYWRlcnMgfHwge30pKSB7XG4gICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgIGRlbGV0ZSBvcHRzLmhlYWRlcnNba2V5XTtcbiAgICB9XG4gIH1cblxuICBsZXQgcmVzcG9uc2U7XG4gIGNvbnN0IHJlcSA9ICh1cmwucHJvdG9jb2wgPT09ICdodHRwczonID8gaHR0cHMucmVxdWVzdCA6IGh0dHAucmVxdWVzdCkodXJsLmhyZWYsIG9wdHMpO1xuICByZXR1cm4gKGFzeW5jICgpID0+IHtcbiAgICBpZiAoanNvbikge1xuICAgICAgc2VuZChyZXEsIEpTT04uc3RyaW5naWZ5KGpzb24pLCAnYXBwbGljYXRpb24vanNvbicpO1xuICAgIH0gZWxzZSBpZiAoZm9ybSkge1xuICAgICAgc2VuZChyZXEsIHF1ZXJ5c3RyaW5nLnN0cmluZ2lmeShmb3JtKSwgJ2FwcGxpY2F0aW9uL3gtd3d3LWZvcm0tdXJsZW5jb2RlZCcpO1xuICAgIH0gZWxzZSBpZiAoYm9keSkge1xuICAgICAgc2VuZChyZXEsIGJvZHkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZW5kKHJlcSk7XG4gICAgfVxuXG4gICAgW3Jlc3BvbnNlXSA9IGF3YWl0IFByb21pc2UucmFjZShbb25jZShyZXEsICdyZXNwb25zZScpLCBvbmNlKHJlcSwgJ3RpbWVvdXQnKV0pO1xuXG4gICAgLy8gdGltZW91dCByZWFjaGVkXG4gICAgaWYgKCFyZXNwb25zZSkge1xuICAgICAgcmVxLmRlc3Ryb3koKTtcbiAgICAgIHRocm93IG5ldyBSUEVycm9yKGBvdXRnb2luZyByZXF1ZXN0IHRpbWVkIG91dCBhZnRlciAke29wdHMudGltZW91dH1tc2ApO1xuICAgIH1cblxuICAgIGNvbnN0IHBhcnRzID0gW107XG5cbiAgICBmb3IgYXdhaXQgKGNvbnN0IHBhcnQgb2YgcmVzcG9uc2UpIHtcbiAgICAgIHBhcnRzLnB1c2gocGFydCk7XG4gICAgfVxuXG4gICAgaWYgKHBhcnRzLmxlbmd0aCkge1xuICAgICAgc3dpdGNoIChyZXNwb25zZVR5cGUpIHtcbiAgICAgICAgY2FzZSAnanNvbic6IHtcbiAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocmVzcG9uc2UsICdib2R5Jywge1xuICAgICAgICAgICAgZ2V0KCkge1xuICAgICAgICAgICAgICBsZXQgdmFsdWUgPSBCdWZmZXIuY29uY2F0KHBhcnRzKTtcbiAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICB2YWx1ZSA9IEpTT04ucGFyc2UodmFsdWUpO1xuICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXJyLCAncmVzcG9uc2UnLCB7IHZhbHVlOiByZXNwb25zZSB9KTtcbiAgICAgICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgICAgICAgIH0gZmluYWxseSB7XG4gICAgICAgICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHJlc3BvbnNlLCAnYm9keScsIHsgdmFsdWUsIGNvbmZpZ3VyYWJsZTogdHJ1ZSB9KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgdW5kZWZpbmVkOlxuICAgICAgICBjYXNlICdidWZmZXInOiB7XG4gICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHJlc3BvbnNlLCAnYm9keScsIHtcbiAgICAgICAgICAgIGdldCgpIHtcbiAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBCdWZmZXIuY29uY2F0KHBhcnRzKTtcbiAgICAgICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHJlc3BvbnNlLCAnYm9keScsIHsgdmFsdWUsIGNvbmZpZ3VyYWJsZTogdHJ1ZSB9KTtcbiAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ3Vuc3VwcG9ydGVkIHJlc3BvbnNlVHlwZSByZXF1ZXN0IG9wdGlvbicpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiByZXNwb25zZTtcbiAgfSkoKVxuICAgIC5jYXRjaCgoZXJyKSA9PiB7XG4gICAgICBpZiAocmVzcG9uc2UpIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlcnIsICdyZXNwb25zZScsIHsgdmFsdWU6IHJlc3BvbnNlIH0pO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH0pXG4gICAgLmZpbmFsbHkoKCkgPT4ge1xuICAgICAgY29uc3QgZHBvcE5vbmNlID0gcmVzcG9uc2UgJiYgcmVzcG9uc2UuaGVhZGVyc1snZHBvcC1ub25jZSddO1xuICAgICAgaWYgKGRwb3BOb25jZSAmJiBOUUNIQVIudGVzdChkcG9wTm9uY2UpKSB7XG4gICAgICAgIG5vbmNlcy5zZXQobm9uY2VLZXksIGRwb3BOb25jZSk7XG4gICAgICB9XG4gICAgfSk7XG59O1xuXG5tb2R1bGUuZXhwb3J0cy5zZXREZWZhdWx0cyA9IHNldERlZmF1bHRzLmJpbmQodW5kZWZpbmVkLCBhbGxvd2VkKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = () => Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcc2lnblxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHVuaXhfdGltZXN0YW1wLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gKCkgPT4gTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFBLHdCQUF3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcc2lnblxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHdlYWtfY2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMua2V5c3RvcmVzID0gbmV3IFdlYWtNYXAoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\n\nfunction hasScheme(input) {\n  if (input.includes('://')) return true;\n\n  const authority = input.replace(/(\\/|\\?)/g, '#').split('#')[0];\n  if (authority.includes(':')) {\n    const index = authority.indexOf(':');\n    const hostOrPort = authority.slice(index + 1);\n    if (!PORT.test(hostOrPort)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction acctSchemeAssumed(input) {\n  if (!input.includes('@')) return false;\n  const parts = input.split('@');\n  const host = parts[parts.length - 1];\n  return !(host.includes(':') || host.includes('/') || host.includes('?'));\n}\n\nfunction normalize(input) {\n  if (typeof input !== 'string') {\n    throw new TypeError('input must be a string');\n  }\n\n  let output;\n  if (hasScheme(input)) {\n    output = input;\n  } else if (acctSchemeAssumed(input)) {\n    output = `acct:${input}`;\n  } else {\n    output = `https://${input}`;\n  }\n\n  return output.split('#')[0];\n}\n\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\n\nmodule.exports = (wwwAuthenticate) => {\n  const params = {};\n  try {\n    while (REGEXP.exec(wwwAuthenticate) !== null) {\n      if (RegExp.$1 && RegExp.$2) {\n        params[RegExp.$1] = RegExp.$2.slice(1, -1);\n      }\n    }\n  } catch (err) {}\n\n  return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7QUFFSjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcd3d3X2F1dGhlbnRpY2F0ZV9wYXJzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgUkVHRVhQID0gLyhcXHcrKT0oXCJbXlwiXSpcIikvZztcblxubW9kdWxlLmV4cG9ydHMgPSAod3d3QXV0aGVudGljYXRlKSA9PiB7XG4gIGNvbnN0IHBhcmFtcyA9IHt9O1xuICB0cnkge1xuICAgIHdoaWxlIChSRUdFWFAuZXhlYyh3d3dBdXRoZW50aWNhdGUpICE9PSBudWxsKSB7XG4gICAgICBpZiAoUmVnRXhwLiQxICYmIFJlZ0V4cC4kMikge1xuICAgICAgICBwYXJhbXNbUmVnRXhwLiQxXSA9IFJlZ0V4cC4kMi5zbGljZSgxLCAtMSk7XG4gICAgICB9XG4gICAgfVxuICB9IGNhdGNoIChlcnIpIHt9XG5cbiAgcmV0dXJuIHBhcmFtcztcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nmodule.exports = {\n  Issuer,\n  Strategy,\n  TokenSet,\n  errors: {\n    OPError,\n    RPError,\n  },\n  custom: {\n    setHttpOptionsDefaults: setDefaults,\n    http_options: HTTP_OPTIONS,\n    clock_tolerance: CLOCK_TOLERANCE,\n  },\n  generators,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxtQkFBTyxDQUFDLGtFQUFVO0FBQ2pDLFFBQVEsbUJBQW1CLEVBQUUsbUJBQU8sQ0FBQyxrRUFBVTtBQUMvQyxpQkFBaUIsbUJBQU8sQ0FBQyx3RkFBcUI7QUFDOUMsaUJBQWlCLG1CQUFPLENBQUMsd0VBQWE7QUFDdEMsUUFBUSxnQ0FBZ0MsRUFBRSxtQkFBTyxDQUFDLGtGQUFrQjtBQUNwRSxtQkFBbUIsbUJBQU8sQ0FBQywwRkFBc0I7QUFDakQsUUFBUSxjQUFjLEVBQUUsbUJBQU8sQ0FBQyxvRkFBbUI7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBJc3N1ZXIgPSByZXF1aXJlKCcuL2lzc3VlcicpO1xuY29uc3QgeyBPUEVycm9yLCBSUEVycm9yIH0gPSByZXF1aXJlKCcuL2Vycm9ycycpO1xuY29uc3QgU3RyYXRlZ3kgPSByZXF1aXJlKCcuL3Bhc3Nwb3J0X3N0cmF0ZWd5Jyk7XG5jb25zdCBUb2tlblNldCA9IHJlcXVpcmUoJy4vdG9rZW5fc2V0Jyk7XG5jb25zdCB7IENMT0NLX1RPTEVSQU5DRSwgSFRUUF9PUFRJT05TIH0gPSByZXF1aXJlKCcuL2hlbHBlcnMvY29uc3RzJyk7XG5jb25zdCBnZW5lcmF0b3JzID0gcmVxdWlyZSgnLi9oZWxwZXJzL2dlbmVyYXRvcnMnKTtcbmNvbnN0IHsgc2V0RGVmYXVsdHMgfSA9IHJlcXVpcmUoJy4vaGVscGVycy9yZXF1ZXN0Jyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBJc3N1ZXIsXG4gIFN0cmF0ZWd5LFxuICBUb2tlblNldCxcbiAgZXJyb3JzOiB7XG4gICAgT1BFcnJvcixcbiAgICBSUEVycm9yLFxuICB9LFxuICBjdXN0b206IHtcbiAgICBzZXRIdHRwT3B0aW9uc0RlZmF1bHRzOiBzZXREZWZhdWx0cyxcbiAgICBodHRwX29wdGlvbnM6IEhUVFBfT1BUSU9OUyxcbiAgICBjbG9ja190b2xlcmFuY2U6IENMT0NLX1RPTEVSQU5DRSxcbiAgfSxcbiAgZ2VuZXJhdG9ycyxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\n\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\n\nconst AAD_MULTITENANT_DISCOVERY = [\n  'https://login.microsoftonline.com/common/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration',\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n  claim_types_supported: ['normal'],\n  claims_parameter_supported: false,\n  grant_types_supported: ['authorization_code', 'implicit'],\n  request_parameter_supported: false,\n  request_uri_parameter_supported: true,\n  require_request_uri_registration: false,\n  response_modes_supported: ['query', 'fragment'],\n  token_endpoint_auth_methods_supported: ['client_secret_basic'],\n};\n\nclass Issuer {\n  #metadata;\n  constructor(meta = {}) {\n    const aadIssValidation = meta[AAD_MULTITENANT];\n    delete meta[AAD_MULTITENANT];\n    ['introspection', 'revocation'].forEach((endpoint) => {\n      // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n      // are defined\n      if (\n        meta[`${endpoint}_endpoint`] &&\n        meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined &&\n        meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined\n      ) {\n        if (meta.token_endpoint_auth_methods_supported) {\n          meta[`${endpoint}_endpoint_auth_methods_supported`] =\n            meta.token_endpoint_auth_methods_supported;\n        }\n        if (meta.token_endpoint_auth_signing_alg_values_supported) {\n          meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] =\n            meta.token_endpoint_auth_signing_alg_values_supported;\n        }\n      }\n    });\n\n    this.#metadata = new Map();\n\n    Object.entries(meta).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true,\n        });\n      }\n    });\n\n    registry.set(this.issuer, this);\n\n    const Client = getClient(this, aadIssValidation);\n\n    Object.defineProperties(this, {\n      Client: { value: Client, enumerable: true },\n      FAPI1Client: { value: class FAPI1Client extends Client {}, enumerable: true },\n      FAPI2Client: { value: class FAPI2Client extends Client {}, enumerable: true },\n    });\n  }\n\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n\n  static async webfinger(input) {\n    const resource = webfingerNormalize(input);\n    const { host } = url.parse(resource);\n    const webfingerUrl = `https://${host}/.well-known/webfinger`;\n\n    const response = await request.call(this, {\n      method: 'GET',\n      url: webfingerUrl,\n      responseType: 'json',\n      searchParams: { resource, rel: 'http://openid.net/specs/connect/1.0/issuer' },\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n\n    const location =\n      Array.isArray(body.links) &&\n      body.links.find(\n        (link) =>\n          typeof link === 'object' &&\n          link.rel === 'http://openid.net/specs/connect/1.0/issuer' &&\n          link.href,\n      );\n\n    if (!location) {\n      throw new RPError({\n        message: 'no issuer found in webfinger response',\n        body,\n      });\n    }\n\n    if (typeof location.href !== 'string' || !location.href.startsWith('https://')) {\n      throw new RPError({\n        printf: ['invalid issuer location %s', location.href],\n        body,\n      });\n    }\n\n    const expectedIssuer = location.href;\n    if (registry.has(expectedIssuer)) {\n      return registry.get(expectedIssuer);\n    }\n\n    const issuer = await this.discover(expectedIssuer);\n\n    if (issuer.issuer !== expectedIssuer) {\n      registry.del(issuer.issuer);\n      throw new RPError(\n        'discovered issuer mismatch, expected %s, got: %s',\n        expectedIssuer,\n        issuer.issuer,\n      );\n    }\n    return issuer;\n  }\n\n  static async discover(uri) {\n    const wellKnownUri = resolveWellKnownUri(uri);\n\n    const response = await request.call(this, {\n      method: 'GET',\n      responseType: 'json',\n      url: wellKnownUri,\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n    return new Issuer({\n      ...ISSUER_DEFAULTS,\n      ...body,\n      [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL) =>\n        wellKnownUri.startsWith(discoveryURL),\n      ),\n    });\n  }\n\n  async reloadJwksUri() {\n    await keystore.call(this, true);\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nfunction resolveWellKnownUri(uri) {\n  const parsed = url.parse(uri);\n  if (parsed.pathname.includes('/.well-known/')) {\n    return uri;\n  } else {\n    let pathname;\n    if (parsed.pathname.endsWith('/')) {\n      pathname = `${parsed.pathname}.well-known/openid-configuration`;\n    } else {\n      pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n    }\n    return url.format({ ...parsed, pathname });\n  }\n}\n\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nmodule.exports = new LRU({ max: 100 });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiJBQUFBLFlBQVksbUJBQU8sQ0FBQywwREFBVzs7QUFFL0IsMkJBQTJCLFVBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxpc3N1ZXJfcmVnaXN0cnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTFJVID0gcmVxdWlyZSgnbHJ1LWNhY2hlJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gbmV3IExSVSh7IG1heDogMTAwIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\n\nfunction verified(err, user, info = {}) {\n  if (err) {\n    this.error(err);\n  } else if (!user) {\n    this.fail(info);\n  } else {\n    this.success(user, info);\n  }\n}\n\nfunction OpenIDConnectStrategy(\n  { client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {},\n  verify,\n) {\n  if (!(client instanceof BaseClient)) {\n    throw new TypeError('client must be an instance of openid-client Client');\n  }\n\n  if (typeof verify !== 'function') {\n    throw new TypeError('verify callback must be a function');\n  }\n\n  if (!client.issuer || !client.issuer.issuer) {\n    throw new TypeError('client must have an issuer with an identifier');\n  }\n\n  this._client = client;\n  this._issuer = client.issuer;\n  this._verify = verify;\n  this._passReqToCallback = passReqToCallback;\n  this._usePKCE = usePKCE;\n  this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n  this._params = cloneDeep(params);\n\n  // state and nonce are handled in authenticate()\n  delete this._params.state;\n  delete this._params.nonce;\n\n  this._extras = cloneDeep(extras);\n\n  if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n  if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n  if (!this._params.scope) this._params.scope = 'openid';\n\n  if (this._usePKCE === true) {\n    const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported)\n      ? this._issuer.code_challenge_methods_supported\n      : false;\n\n    if (supportedMethods && supportedMethods.includes('S256')) {\n      this._usePKCE = 'S256';\n    } else if (supportedMethods && supportedMethods.includes('plain')) {\n      this._usePKCE = 'plain';\n    } else if (supportedMethods) {\n      throw new TypeError(\n        'neither code_challenge_method supported by the client is supported by the issuer',\n      );\n    } else {\n      this._usePKCE = 'S256';\n    }\n  } else if (typeof this._usePKCE === 'string' && !['plain', 'S256'].includes(this._usePKCE)) {\n    throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n  }\n\n  this.name = url.parse(client.issuer.issuer).hostname;\n}\n\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n  (async () => {\n    const client = this._client;\n    if (!req.session) {\n      throw new TypeError('authentication requires session support');\n    }\n    const reqParams = client.callbackParams(req);\n    const sessionKey = this._key;\n\n    const { 0: parameter, length } = Object.keys(reqParams);\n\n    /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */\n    if (length === 0 || (length === 1 && parameter === 'iss')) {\n      // provide options object with extra authentication parameters\n      const params = {\n        state: random(),\n        ...this._params,\n        ...options,\n      };\n\n      if (!params.nonce && params.response_type.includes('id_token')) {\n        params.nonce = random();\n      }\n\n      req.session[sessionKey] = pick(params, 'nonce', 'state', 'max_age', 'response_type');\n\n      if (this._usePKCE && params.response_type.includes('code')) {\n        const verifier = random();\n        req.session[sessionKey].code_verifier = verifier;\n\n        switch (this._usePKCE) {\n          case 'S256':\n            params.code_challenge = codeChallenge(verifier);\n            params.code_challenge_method = 'S256';\n            break;\n          case 'plain':\n            params.code_challenge = verifier;\n            break;\n        }\n      }\n\n      this.redirect(client.authorizationUrl(params));\n      return;\n    }\n    /* end authentication request */\n\n    /* start authentication response */\n\n    const session = req.session[sessionKey];\n    if (Object.keys(session || {}).length === 0) {\n      throw new Error(\n        format(\n          'did not find expected authorization request details in session, req.session[\"%s\"] is %j',\n          sessionKey,\n          session,\n        ),\n      );\n    }\n\n    const {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    } = session;\n\n    try {\n      delete req.session[sessionKey];\n    } catch (err) {}\n\n    const opts = {\n      redirect_uri: this._params.redirect_uri,\n      ...options,\n    };\n\n    const checks = {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    };\n\n    const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n\n    const passReq = this._passReqToCallback;\n    const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n\n    const args = [tokenset, verified.bind(this)];\n\n    if (loadUserinfo) {\n      if (!tokenset.access_token) {\n        throw new RPError({\n          message:\n            'expected access_token to be returned when asking for userinfo in verify callback',\n          tokenset,\n        });\n      }\n      const userinfo = await client.userinfo(tokenset);\n      args.splice(1, 0, userinfo);\n    }\n\n    if (passReq) {\n      args.unshift(req);\n    }\n\n    this._verify(...args);\n    /* end authentication response */\n  })().catch((error) => {\n    if (\n      (error instanceof OPError &&\n        error.error !== 'server_error' &&\n        !error.error.startsWith('invalid')) ||\n      error instanceof RPError\n    ) {\n      this.fail(error);\n    } else {\n      this.error(error);\n    }\n  });\n};\n\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass TokenSet {\n  constructor(values) {\n    Object.assign(this, values);\n    const { constructor, ...properties } = Object.getOwnPropertyDescriptors(\n      this.constructor.prototype,\n    );\n\n    Object.defineProperties(this, properties);\n  }\n\n  set expires_in(value) {\n    this.expires_at = now() + Number(value);\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  claims() {\n    if (!this.id_token) {\n      throw new TypeError('id_token not present in TokenSet');\n    }\n\n    return JSON.parse(base64url.decode(this.id_token.split('.')[1]));\n  }\n}\n\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvdG9rZW5fc2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLHdGQUFxQjtBQUMvQyxZQUFZLG1CQUFPLENBQUMsa0dBQTBCOztBQUU5QztBQUNBO0FBQ0E7QUFDQSxZQUFZLDZCQUE2QjtBQUN6QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcdG9rZW5fc2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vaGVscGVycy9iYXNlNjR1cmwnKTtcbmNvbnN0IG5vdyA9IHJlcXVpcmUoJy4vaGVscGVycy91bml4X3RpbWVzdGFtcCcpO1xuXG5jbGFzcyBUb2tlblNldCB7XG4gIGNvbnN0cnVjdG9yKHZhbHVlcykge1xuICAgIE9iamVjdC5hc3NpZ24odGhpcywgdmFsdWVzKTtcbiAgICBjb25zdCB7IGNvbnN0cnVjdG9yLCAuLi5wcm9wZXJ0aWVzIH0gPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhcbiAgICAgIHRoaXMuY29uc3RydWN0b3IucHJvdG90eXBlLFxuICAgICk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydGllcyh0aGlzLCBwcm9wZXJ0aWVzKTtcbiAgfVxuXG4gIHNldCBleHBpcmVzX2luKHZhbHVlKSB7XG4gICAgdGhpcy5leHBpcmVzX2F0ID0gbm93KCkgKyBOdW1iZXIodmFsdWUpO1xuICB9XG5cbiAgZ2V0IGV4cGlyZXNfaW4oKSB7XG4gICAgcmV0dXJuIE1hdGgubWF4LmFwcGx5KG51bGwsIFt0aGlzLmV4cGlyZXNfYXQgLSBub3coKSwgMF0pO1xuICB9XG5cbiAgZXhwaXJlZCgpIHtcbiAgICByZXR1cm4gdGhpcy5leHBpcmVzX2luID09PSAwO1xuICB9XG5cbiAgY2xhaW1zKCkge1xuICAgIGlmICghdGhpcy5pZF90b2tlbikge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignaWRfdG9rZW4gbm90IHByZXNlbnQgaW4gVG9rZW5TZXQnKTtcbiAgICB9XG5cbiAgICByZXR1cm4gSlNPTi5wYXJzZShiYXNlNjR1cmwuZGVjb2RlKHRoaXMuaWRfdG9rZW4uc3BsaXQoJy4nKVsxXSkpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gVG9rZW5TZXQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;