'use client'

import { useSession, signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { DocumentWithUser } from '@/types/database'
import { formatDate, getStatusColor, getUrgencyColor } from '@/lib/utils'
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  MessageSquare,
  LogOut,
  User,
  Download,
  Eye,
  Upload,
  AlertCircle,
  Filter
} from 'lucide-react'

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [documents, setDocuments] = useState<DocumentWithUser[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<DocumentWithUser[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDocument, setSelectedDocument] = useState<DocumentWithUser | null>(null)
  const [signModalOpen, setSignModalOpen] = useState(false)
  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false)
  const [signedFile, setSignedFile] = useState<File | null>(null)
  const [feedback, setFeedback] = useState('')
  const [actionLoading, setActionLoading] = useState(false)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [urgencyFilter, setUrgencyFilter] = useState<string>('all')
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    signed: 0,
    feedback: 0,
    urgent: 0
  })

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'admin') {
      router.push('/dashboard')
      return
    }

    fetchDocuments()
  }, [session, status, router])

  useEffect(() => {
    // Apply filters
    let filtered = documents

    if (statusFilter !== 'all') {
      filtered = filtered.filter(doc => doc.status === statusFilter)
    }

    if (urgencyFilter !== 'all') {
      filtered = filtered.filter(doc => doc.urgency === urgencyFilter)
    }

    setFilteredDocuments(filtered)
  }, [documents, statusFilter, urgencyFilter])

  const fetchDocuments = async () => {
    try {
      const response = await fetch('/api/documents')
      if (response.ok) {
        const data = await response.json()
        setDocuments(data.documents)
        
        // Calculate stats
        const newStats = {
          total: data.documents.length,
          pending: data.documents.filter((d: DocumentWithUser) => d.status === 'pending').length,
          signed: data.documents.filter((d: DocumentWithUser) => d.status === 'signed').length,
          feedback: data.documents.filter((d: DocumentWithUser) => d.status === 'feedback').length,
          urgent: data.documents.filter((d: DocumentWithUser) => d.urgency === 'urgent' && d.status === 'pending').length
        }
        setStats(newStats)
      }
    } catch (error) {
      console.error('Error fetching documents:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSignDocument = async () => {
    if (!selectedDocument || !signedFile) return

    setActionLoading(true)
    try {
      const formData = new FormData()
      formData.append('action', 'sign')
      formData.append('signedFile', signedFile)

      const response = await fetch(`/api/documents/${selectedDocument.id}`, {
        method: 'PUT',
        body: formData,
      })

      if (response.ok) {
        await fetchDocuments()
        setSignModalOpen(false)
        setSelectedDocument(null)
        setSignedFile(null)
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Failed to sign document')
      }
    } catch (error) {
      console.error('Error signing document:', error)
      alert('Failed to sign document')
    } finally {
      setActionLoading(false)
    }
  }

  const handleProvideFeedback = async () => {
    if (!selectedDocument || !feedback.trim()) return

    setActionLoading(true)
    try {
      const formData = new FormData()
      formData.append('action', 'feedback')
      formData.append('feedback', feedback.trim())

      const response = await fetch(`/api/documents/${selectedDocument.id}`, {
        method: 'PUT',
        body: formData,
      })

      if (response.ok) {
        await fetchDocuments()
        setFeedbackModalOpen(false)
        setSelectedDocument(null)
        setFeedback('')
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Failed to provide feedback')
      }
    } catch (error) {
      console.error('Error providing feedback:', error)
      alert('Failed to provide feedback')
    } finally {
      setActionLoading(false)
    }
  }

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'signed':
        return <CheckCircle className="h-4 w-4" />
      case 'rejected':
        return <XCircle className="h-4 w-4" />
      case 'feedback':
        return <MessageSquare className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!session || session.user.role !== 'admin') {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Admin Dashboard
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-700">{session.user.name}</span>
                <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                  {session.user.role}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Signed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.signed}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Feedback Given</CardTitle>
              <MessageSquare className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.feedback}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Urgent Pending</CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.urgent}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="signed">Signed</option>
                  <option value="feedback">Feedback Given</option>
                </select>
              </div>
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Urgency</label>
                <select
                  value={urgencyFilter}
                  onChange={(e) => setUrgencyFilter(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="all">All Urgency Levels</option>
                  <option value="urgent">Urgent</option>
                  <option value="neutral">Neutral</option>
                  <option value="none">None</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Documents List */}
        <Card>
          <CardHeader>
            <CardTitle>All Documents</CardTitle>
            <CardDescription>
              Review and manage document signing requests ({filteredDocuments.length} of {documents.length} documents)
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredDocuments.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {documents.length === 0 ? 'No documents found' : 'No documents match the current filters'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredDocuments.map((document) => (
                  <div
                    key={document.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-medium text-gray-900">{document.subject}</h3>
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                          {getStatusIcon(document.status)}
                          {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                        </span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(document.urgency)}`}>
                          {document.urgency.charAt(0).toUpperCase() + document.urgency.slice(1)}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{document.details}</p>
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500 mb-2">
                        <span>User: {document.user.name} ({document.user.email})</span>
                        <span>Uploaded: {formatDate(document.created_at)}</span>
                        {document.signed_at && (
                          <span>Signed: {formatDate(document.signed_at)}</span>
                        )}
                      </div>
                      
                      {document.feedback && (
                        <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                          <strong>Previous Feedback:</strong> {document.feedback}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(document.original_file_url, '_blank')}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      
                      {document.status === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => {
                              setSelectedDocument(document)
                              setSignModalOpen(true)
                            }}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Sign
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedDocument(document)
                              setFeedbackModalOpen(true)
                            }}
                          >
                            <MessageSquare className="h-4 w-4 mr-1" />
                            Feedback
                          </Button>
                        </>
                      )}
                      
                      {document.signed_file_url && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(document.signed_file_url, '_blank')}
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Signed
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sign Document Modal */}
      <Dialog open={signModalOpen} onOpenChange={setSignModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sign Document</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Upload the signed version of: <strong>{selectedDocument?.subject}</strong>
            </p>
            <Input
              type="file"
              onChange={(e) => setSignedFile(e.target.files?.[0] || null)}
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setSignModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSignDocument}
              disabled={!signedFile || actionLoading}
            >
              {actionLoading ? 'Signing...' : 'Sign Document'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Feedback Modal */}
      <Dialog open={feedbackModalOpen} onOpenChange={setFeedbackModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Provide Feedback</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Provide feedback for: <strong>{selectedDocument?.subject}</strong>
            </p>
            <Textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Enter your feedback or request for additional information..."
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setFeedbackModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleProvideFeedback}
              disabled={!feedback.trim() || actionLoading}
            >
              {actionLoading ? 'Sending...' : 'Send Feedback'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
