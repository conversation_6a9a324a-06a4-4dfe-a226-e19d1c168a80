/**
 * Google Apps Script for Document Signing System
 * This script should be added to your Google Sheets to automate document tracking
 */

// Configuration - Update these with your actual values
const CONFIG = {
  SHEET_NAME: 'Document Tracking',
  WEBHOOK_URL: 'https://your-app-domain.vercel.app/api/webhook/sheets', // Optional webhook for notifications
  DRIVE_FOLDER_ID: 'your-google-drive-folder-id'
}

/**
 * Initialize the sheet with proper headers
 */
function initializeSheet() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME) || 
                SpreadsheetApp.getActiveSpreadsheet().insertSheet(CONFIG.SHEET_NAME)
  
  // Set headers
  const headers = [
    'Timestamp',
    'User Name',
    'User Email', 
    'Subject',
    'Details',
    'Urgency',
    'Status',
    'Original Document Link',
    'Signed Document Link',
    'Admin Name',
    'Feedback',
    'Signed Date'
  ]
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers])
  sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold')
  sheet.getRange(1, 1, 1, headers.length).setBackground('#4285f4')
  sheet.getRange(1, 1, 1, headers.length).setFontColor('white')
  
  // Auto-resize columns
  sheet.autoResizeColumns(1, headers.length)
  
  Logger.log('Sheet initialized successfully')
}

/**
 * Add a new document entry
 */
function addDocumentEntry(data) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME)
  if (!sheet) {
    initializeSheet()
    return addDocumentEntry(data)
  }
  
  const newRow = [
    data.timestamp || new Date(),
    data.userName || '',
    data.userEmail || '',
    data.subject || '',
    data.details || '',
    data.urgency || 'none',
    data.status || 'pending',
    data.originalDocumentLink || '',
    data.signedDocumentLink || '',
    data.adminName || '',
    data.feedback || '',
    data.signedDate || ''
  ]
  
  sheet.appendRow(newRow)
  
  // Format the new row
  const lastRow = sheet.getLastRow()
  formatRow(sheet, lastRow, data.status)
  
  Logger.log(`Added new document entry: ${data.subject}`)
}

/**
 * Update an existing document entry
 */
function updateDocumentEntry(userEmail, subject, updateData) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME)
  if (!sheet) return false
  
  const data = sheet.getDataRange().getValues()
  
  // Find the row to update (skip header row)
  for (let i = 1; i < data.length; i++) {
    if (data[i][2] === userEmail && data[i][3] === subject) { // Match by email and subject
      const rowNum = i + 1
      
      // Update specific columns based on updateData
      if (updateData.status) {
        sheet.getRange(rowNum, 7).setValue(updateData.status) // Status column
        formatRow(sheet, rowNum, updateData.status)
      }
      if (updateData.signedDocumentLink) {
        sheet.getRange(rowNum, 9).setValue(updateData.signedDocumentLink) // Signed document link
      }
      if (updateData.adminName) {
        sheet.getRange(rowNum, 10).setValue(updateData.adminName) // Admin name
      }
      if (updateData.feedback) {
        sheet.getRange(rowNum, 11).setValue(updateData.feedback) // Feedback
      }
      if (updateData.signedDate) {
        sheet.getRange(rowNum, 12).setValue(updateData.signedDate) // Signed date
      }
      
      Logger.log(`Updated document entry: ${subject}`)
      return true
    }
  }
  
  Logger.log(`Document entry not found: ${subject}`)
  return false
}

/**
 * Format row based on status
 */
function formatRow(sheet, rowNum, status) {
  const range = sheet.getRange(rowNum, 1, 1, sheet.getLastColumn())
  
  switch (status) {
    case 'pending':
      range.setBackground('#fff3cd') // Light yellow
      break
    case 'signed':
      range.setBackground('#d4edda') // Light green
      break
    case 'rejected':
      range.setBackground('#f8d7da') // Light red
      break
    case 'feedback':
      range.setBackground('#d1ecf1') // Light blue
      break
    default:
      range.setBackground('#ffffff') // White
  }
}

/**
 * Get document statistics
 */
function getDocumentStats() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME)
  if (!sheet) return {}
  
  const data = sheet.getDataRange().getValues()
  const stats = {
    total: data.length - 1, // Exclude header
    pending: 0,
    signed: 0,
    rejected: 0,
    feedback: 0
  }
  
  // Count by status (skip header row)
  for (let i = 1; i < data.length; i++) {
    const status = data[i][6] // Status column
    if (stats.hasOwnProperty(status)) {
      stats[status]++
    }
  }
  
  return stats
}

/**
 * Send email notification for urgent documents
 */
function checkUrgentDocuments() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME)
  if (!sheet) return
  
  const data = sheet.getDataRange().getValues()
  const urgentPending = []
  
  // Find urgent pending documents (skip header row)
  for (let i = 1; i < data.length; i++) {
    const urgency = data[i][5] // Urgency column
    const status = data[i][6] // Status column
    
    if (urgency === 'urgent' && status === 'pending') {
      urgentPending.push({
        subject: data[i][3],
        userName: data[i][1],
        userEmail: data[i][2],
        timestamp: data[i][0]
      })
    }
  }
  
  if (urgentPending.length > 0) {
    sendUrgentNotification(urgentPending)
  }
}

/**
 * Send notification for urgent documents
 */
function sendUrgentNotification(urgentDocs) {
  const adminEmail = '<EMAIL>' // Update with actual admin email
  const subject = `Urgent Documents Pending Signature (${urgentDocs.length})`
  
  let body = 'The following urgent documents are pending signature:\n\n'
  urgentDocs.forEach(doc => {
    body += `• ${doc.subject} - ${doc.userName} (${doc.userEmail}) - ${doc.timestamp}\n`
  })
  
  body += '\nPlease review and sign these documents as soon as possible.'
  
  try {
    GmailApp.sendEmail(adminEmail, subject, body)
    Logger.log(`Sent urgent notification for ${urgentDocs.length} documents`)
  } catch (error) {
    Logger.log(`Error sending email: ${error.toString()}`)
  }
}

/**
 * Set up time-based triggers
 */
function setupTriggers() {
  // Delete existing triggers
  const triggers = ScriptApp.getProjectTriggers()
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger))
  
  // Check for urgent documents every hour
  ScriptApp.newTrigger('checkUrgentDocuments')
    .timeBased()
    .everyHours(1)
    .create()
  
  Logger.log('Triggers set up successfully')
}

/**
 * Manual setup function - run this once to initialize everything
 */
function setup() {
  initializeSheet()
  setupTriggers()
  Logger.log('Setup completed successfully')
}

/**
 * Test function to add sample data
 */
function testAddEntry() {
  const testData = {
    timestamp: new Date(),
    userName: 'John Doe',
    userEmail: '<EMAIL>',
    subject: 'Test Document',
    details: 'This is a test document for signing',
    urgency: 'neutral',
    status: 'pending',
    originalDocumentLink: 'https://drive.google.com/file/d/test123/view'
  }
  
  addDocumentEntry(testData)
  Logger.log('Test entry added successfully')
}
