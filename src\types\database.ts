export interface User {
  id: string
  email: string
  name: string
  role: 'user' | 'admin'
  created_at: string
  updated_at: string
}

export interface Document {
  id: string
  user_id: string
  subject: string
  details: string
  urgency: 'urgent' | 'neutral' | 'none'
  status: 'pending' | 'signed' | 'rejected' | 'feedback'
  original_filename: string
  original_file_url: string
  original_drive_id: string
  signed_filename?: string
  signed_file_url?: string
  signed_drive_id?: string
  feedback?: string
  created_at: string
  updated_at: string
  signed_at?: string
  admin_id?: string
}

export interface DocumentWithUser extends Document {
  user: User
  admin?: User
}

export type DocumentStatus = 'pending' | 'signed' | 'rejected' | 'feedback'
export type UrgencyLevel = 'urgent' | 'neutral' | 'none'
export type UserRole = 'user' | 'admin'
