"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gtoken";
exports.ids = ["vendor-chunks/gtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/gtoken/build/cjs/src/index.cjs":
/*!*****************************************************!*\
  !*** ./node_modules/gtoken/build/cjs/src/index.cjs ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.GoogleToken = void 0;\nvar fs = _interopRequireWildcard(__webpack_require__(/*! fs */ \"fs\"));\nvar _gaxios = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nvar jws = _interopRequireWildcard(__webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\"));\nvar path = _interopRequireWildcard(__webpack_require__(/*! path */ \"path\"));\nvar _util = __webpack_require__(/*! util */ \"util\");\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, \"default\": e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t3 in e) \"default\" !== _t3 && {}.hasOwnProperty.call(e, _t3) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t3)) && (i.get || i.set) ? o(f, _t3, i) : f[_t3] = e[_t3]); return f; })(e, t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classPrivateMethodInitSpec(e, a) { _checkPrivateRedeclaration(e, a), a.add(e); }\nfunction _classPrivateFieldInitSpec(e, t, a) { _checkPrivateRedeclaration(e, t), t.set(e, a); }\nfunction _checkPrivateRedeclaration(e, t) { if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\"); }\nfunction _classPrivateFieldSet(s, a, r) { return s.set(_assertClassBrand(s, a), r), r; }\nfunction _classPrivateFieldGet(s, a) { return s.get(_assertClassBrand(s, a)); }\nfunction _assertClassBrand(e, t, n) { if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n; throw new TypeError(\"Private element is not present on this object\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(t, e) { if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e; if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\"); return _assertThisInitialized(t); }\nfunction _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); return e; }\nfunction _inherits(t, e) { if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, \"prototype\", { writable: !1 }), e && _setPrototypeOf(t, e); }\nfunction _wrapNativeSuper(t) { var r = \"function\" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }\nfunction _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf(\"[native code]\"); } catch (n) { return \"function\" == typeof t; } }\nfunction _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }\nfunction _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i[\"return\"]) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2); } }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; } /**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nvar readFile = fs.readFile ? (0, _util.promisify)(fs.readFile) : /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n  return _regenerator().w(function (_context) {\n    while (1) switch (_context.n) {\n      case 0:\n        throw new ErrorWithCode('use key rather than keyFile.', 'MISSING_CREDENTIALS');\n      case 1:\n        return _context.a(2);\n    }\n  }, _callee);\n}));\nvar GOOGLE_TOKEN_URL = 'https://oauth2.googleapis.com/token';\nvar GOOGLE_REVOKE_TOKEN_URL = 'https://oauth2.googleapis.com/revoke?token=';\nvar ErrorWithCode = /*#__PURE__*/function (_Error) {\n  function ErrorWithCode(message, code) {\n    var _this;\n    _classCallCheck(this, ErrorWithCode);\n    _this = _callSuper(this, ErrorWithCode, [message]);\n    _defineProperty(_this, \"code\", void 0);\n    _this.code = code;\n    return _this;\n  }\n  _inherits(ErrorWithCode, _Error);\n  return _createClass(ErrorWithCode);\n}(/*#__PURE__*/_wrapNativeSuper(Error));\nvar _inFlightRequest = /*#__PURE__*/new WeakMap();\nvar _GoogleToken_brand = /*#__PURE__*/new WeakSet();\nvar GoogleToken = exports.GoogleToken = /*#__PURE__*/function () {\n  /**\n   * Create a GoogleToken.\n   *\n   * @param options  Configuration object.\n   */\n  function GoogleToken(_options) {\n    _classCallCheck(this, GoogleToken);\n    _classPrivateMethodInitSpec(this, _GoogleToken_brand);\n    _defineProperty(this, \"expiresAt\", void 0);\n    _defineProperty(this, \"key\", void 0);\n    _defineProperty(this, \"keyFile\", void 0);\n    _defineProperty(this, \"iss\", void 0);\n    _defineProperty(this, \"sub\", void 0);\n    _defineProperty(this, \"scope\", void 0);\n    _defineProperty(this, \"rawToken\", void 0);\n    _defineProperty(this, \"tokenExpires\", void 0);\n    _defineProperty(this, \"email\", void 0);\n    _defineProperty(this, \"additionalClaims\", void 0);\n    _defineProperty(this, \"eagerRefreshThresholdMillis\", void 0);\n    _defineProperty(this, \"transporter\", {\n      request: function request(opts) {\n        return (0, _gaxios.request)(opts);\n      }\n    });\n    _classPrivateFieldInitSpec(this, _inFlightRequest, void 0);\n    _assertClassBrand(_GoogleToken_brand, this, _configure).call(this, _options);\n  }\n\n  /**\n   * Returns whether the token has expired.\n   *\n   * @return true if the token has expired, false otherwise.\n   */\n  return _createClass(GoogleToken, [{\n    key: \"accessToken\",\n    get: function get() {\n      return this.rawToken ? this.rawToken.access_token : undefined;\n    }\n  }, {\n    key: \"idToken\",\n    get: function get() {\n      return this.rawToken ? this.rawToken.id_token : undefined;\n    }\n  }, {\n    key: \"tokenType\",\n    get: function get() {\n      return this.rawToken ? this.rawToken.token_type : undefined;\n    }\n  }, {\n    key: \"refreshToken\",\n    get: function get() {\n      return this.rawToken ? this.rawToken.refresh_token : undefined;\n    }\n  }, {\n    key: \"hasExpired\",\n    value: function hasExpired() {\n      var now = new Date().getTime();\n      if (this.rawToken && this.expiresAt) {\n        return now >= this.expiresAt;\n      } else {\n        return true;\n      }\n    }\n\n    /**\n     * Returns whether the token will expire within eagerRefreshThresholdMillis\n     *\n     * @return true if the token will be expired within eagerRefreshThresholdMillis, false otherwise.\n     */\n  }, {\n    key: \"isTokenExpiring\",\n    value: function isTokenExpiring() {\n      var _this$eagerRefreshThr;\n      var now = new Date().getTime();\n      var eagerRefreshThresholdMillis = (_this$eagerRefreshThr = this.eagerRefreshThresholdMillis) !== null && _this$eagerRefreshThr !== void 0 ? _this$eagerRefreshThr : 0;\n      if (this.rawToken && this.expiresAt) {\n        return this.expiresAt <= now + eagerRefreshThresholdMillis;\n      } else {\n        return true;\n      }\n    }\n\n    /**\n     * Returns a cached token or retrieves a new one from Google.\n     *\n     * @param callback The callback function.\n     */\n  }, {\n    key: \"getToken\",\n    value: function getToken(callback) {\n      var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (_typeof(callback) === 'object') {\n        opts = callback;\n        callback = undefined;\n      }\n      opts = Object.assign({\n        forceRefresh: false\n      }, opts);\n      if (callback) {\n        var cb = callback;\n        _assertClassBrand(_GoogleToken_brand, this, _getTokenAsync).call(this, opts).then(function (t) {\n          return cb(null, t);\n        }, callback);\n        return;\n      }\n      return _assertClassBrand(_GoogleToken_brand, this, _getTokenAsync).call(this, opts);\n    }\n\n    /**\n     * Given a keyFile, extract the key and client email if available\n     * @param keyFile Path to a json, pem, or p12 file that contains the key.\n     * @returns an object with privateKey and clientEmail properties\n     */\n  }, {\n    key: \"getCredentials\",\n    value: (function () {\n      var _getCredentials = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(keyFile) {\n        var ext, key, body, privateKey, clientEmail, _privateKey, _t;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              ext = path.extname(keyFile);\n              _t = ext;\n              _context2.n = _t === '.json' ? 1 : _t === '.der' ? 4 : _t === '.crt' ? 4 : _t === '.pem' ? 4 : _t === '.p12' ? 6 : _t === '.pfx' ? 6 : 7;\n              break;\n            case 1:\n              _context2.n = 2;\n              return readFile(keyFile, 'utf8');\n            case 2:\n              key = _context2.v;\n              body = JSON.parse(key);\n              privateKey = body.private_key;\n              clientEmail = body.client_email;\n              if (!(!privateKey || !clientEmail)) {\n                _context2.n = 3;\n                break;\n              }\n              throw new ErrorWithCode('private_key and client_email are required.', 'MISSING_CREDENTIALS');\n            case 3:\n              return _context2.a(2, {\n                privateKey: privateKey,\n                clientEmail: clientEmail\n              });\n            case 4:\n              _context2.n = 5;\n              return readFile(keyFile, 'utf8');\n            case 5:\n              _privateKey = _context2.v;\n              return _context2.a(2, {\n                privateKey: _privateKey\n              });\n            case 6:\n              throw new ErrorWithCode('*.p12 certificates are not supported after v6.1.2. ' + 'Consider utilizing *.json format or converting *.p12 to *.pem using the OpenSSL CLI.', 'UNKNOWN_CERTIFICATE_TYPE');\n            case 7:\n              throw new ErrorWithCode('Unknown certificate type. Type is determined based on file extension. ' + 'Current supported extensions are *.json, and *.pem.', 'UNKNOWN_CERTIFICATE_TYPE');\n            case 8:\n              return _context2.a(2);\n          }\n        }, _callee2);\n      }));\n      function getCredentials(_x) {\n        return _getCredentials.apply(this, arguments);\n      }\n      return getCredentials;\n    }())\n  }, {\n    key: \"revokeToken\",\n    value: function revokeToken(callback) {\n      if (callback) {\n        _assertClassBrand(_GoogleToken_brand, this, _revokeTokenAsync).call(this).then(function () {\n          return callback();\n        }, callback);\n        return;\n      }\n      return _assertClassBrand(_GoogleToken_brand, this, _revokeTokenAsync).call(this);\n    }\n  }]);\n}();\nfunction _getTokenAsync(_x2) {\n  return _getTokenAsync2.apply(this, arguments);\n}\nfunction _getTokenAsync2() {\n  _getTokenAsync2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(opts) {\n    return _regenerator().w(function (_context3) {\n      while (1) switch (_context3.n) {\n        case 0:\n          if (!(_classPrivateFieldGet(_inFlightRequest, this) && !opts.forceRefresh)) {\n            _context3.n = 1;\n            break;\n          }\n          return _context3.a(2, _classPrivateFieldGet(_inFlightRequest, this));\n        case 1:\n          _context3.p = 1;\n          _context3.n = 2;\n          return _classPrivateFieldSet(_inFlightRequest, this, _assertClassBrand(_GoogleToken_brand, this, _getTokenAsyncInner).call(this, opts));\n        case 2:\n          return _context3.a(2, _context3.v);\n        case 3:\n          _context3.p = 3;\n          _classPrivateFieldSet(_inFlightRequest, this, undefined);\n          return _context3.f(3);\n        case 4:\n          return _context3.a(2);\n      }\n    }, _callee3, this, [[1,, 3, 4]]);\n  }));\n  return _getTokenAsync2.apply(this, arguments);\n}\nfunction _getTokenAsyncInner(_x3) {\n  return _getTokenAsyncInner2.apply(this, arguments);\n}\nfunction _getTokenAsyncInner2() {\n  _getTokenAsyncInner2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(opts) {\n    var creds;\n    return _regenerator().w(function (_context4) {\n      while (1) switch (_context4.n) {\n        case 0:\n          if (!(this.isTokenExpiring() === false && opts.forceRefresh === false)) {\n            _context4.n = 1;\n            break;\n          }\n          return _context4.a(2, Promise.resolve(this.rawToken));\n        case 1:\n          if (!(!this.key && !this.keyFile)) {\n            _context4.n = 2;\n            break;\n          }\n          throw new Error('No key or keyFile set.');\n        case 2:\n          if (!(!this.key && this.keyFile)) {\n            _context4.n = 4;\n            break;\n          }\n          _context4.n = 3;\n          return this.getCredentials(this.keyFile);\n        case 3:\n          creds = _context4.v;\n          this.key = creds.privateKey;\n          this.iss = creds.clientEmail || this.iss;\n          if (!creds.clientEmail) {\n            _assertClassBrand(_GoogleToken_brand, this, _ensureEmail).call(this);\n          }\n        case 4:\n          return _context4.a(2, _assertClassBrand(_GoogleToken_brand, this, _requestToken).call(this));\n      }\n    }, _callee4, this);\n  }));\n  return _getTokenAsyncInner2.apply(this, arguments);\n}\nfunction _ensureEmail() {\n  if (!this.iss) {\n    throw new ErrorWithCode('email is required.', 'MISSING_CREDENTIALS');\n  }\n}\nfunction _revokeTokenAsync() {\n  return _revokeTokenAsync2.apply(this, arguments);\n}\nfunction _revokeTokenAsync2() {\n  _revokeTokenAsync2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n    var url;\n    return _regenerator().w(function (_context5) {\n      while (1) switch (_context5.n) {\n        case 0:\n          if (this.accessToken) {\n            _context5.n = 1;\n            break;\n          }\n          throw new Error('No token to revoke.');\n        case 1:\n          url = GOOGLE_REVOKE_TOKEN_URL + this.accessToken;\n          _context5.n = 2;\n          return this.transporter.request({\n            url: url,\n            retry: true\n          });\n        case 2:\n          _assertClassBrand(_GoogleToken_brand, this, _configure).call(this, {\n            email: this.iss,\n            sub: this.sub,\n            key: this.key,\n            keyFile: this.keyFile,\n            scope: this.scope,\n            additionalClaims: this.additionalClaims\n          });\n        case 3:\n          return _context5.a(2);\n      }\n    }, _callee5, this);\n  }));\n  return _revokeTokenAsync2.apply(this, arguments);\n}\n/**\n * Configure the GoogleToken for re-use.\n * @param  {object} options Configuration object.\n */\nfunction _configure() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  this.keyFile = options.keyFile;\n  this.key = options.key;\n  this.rawToken = undefined;\n  this.iss = options.email || options.iss;\n  this.sub = options.sub;\n  this.additionalClaims = options.additionalClaims;\n  if (_typeof(options.scope) === 'object') {\n    this.scope = options.scope.join(' ');\n  } else {\n    this.scope = options.scope;\n  }\n  this.eagerRefreshThresholdMillis = options.eagerRefreshThresholdMillis;\n  if (options.transporter) {\n    this.transporter = options.transporter;\n  }\n}\n/**\n * Request the token from Google.\n */\nfunction _requestToken() {\n  return _requestToken2.apply(this, arguments);\n}\nfunction _requestToken2() {\n  _requestToken2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {\n    var iat, additionalClaims, payload, signedJWT, r, _response, _response2, body, desc, _t2;\n    return _regenerator().w(function (_context6) {\n      while (1) switch (_context6.n) {\n        case 0:\n          iat = Math.floor(new Date().getTime() / 1000);\n          additionalClaims = this.additionalClaims || {};\n          payload = Object.assign({\n            iss: this.iss,\n            scope: this.scope,\n            aud: GOOGLE_TOKEN_URL,\n            exp: iat + 3600,\n            iat: iat,\n            sub: this.sub\n          }, additionalClaims);\n          signedJWT = jws.sign({\n            header: {\n              alg: 'RS256'\n            },\n            payload: payload,\n            secret: this.key\n          });\n          _context6.p = 1;\n          _context6.n = 2;\n          return this.transporter.request({\n            method: 'POST',\n            url: GOOGLE_TOKEN_URL,\n            data: new URLSearchParams({\n              grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',\n              assertion: signedJWT\n            }),\n            responseType: 'json',\n            retryConfig: {\n              httpMethodsToRetry: ['POST']\n            }\n          });\n        case 2:\n          r = _context6.v;\n          this.rawToken = r.data;\n          this.expiresAt = r.data.expires_in === null || r.data.expires_in === undefined ? undefined : (iat + r.data.expires_in) * 1000;\n          return _context6.a(2, this.rawToken);\n        case 3:\n          _context6.p = 3;\n          _t2 = _context6.v;\n          this.rawToken = undefined;\n          this.tokenExpires = undefined;\n          body = _t2.response && (_response = _t2.response) !== null && _response !== void 0 && _response.data ? (_response2 = _t2.response) === null || _response2 === void 0 ? void 0 : _response2.data : {};\n          if (body.error) {\n            desc = body.error_description ? \": \".concat(body.error_description) : '';\n            _t2.message = \"\".concat(body.error).concat(desc);\n          }\n          throw _t2;\n        case 4:\n          return _context6.a(2);\n      }\n    }, _callee6, this, [[1, 3]]);\n  }));\n  return _requestToken2.apply(this, arguments);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gtoken/build/cjs/src/index.cjs\n");

/***/ })

};
;