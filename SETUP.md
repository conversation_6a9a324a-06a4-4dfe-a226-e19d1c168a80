# Document Signing System - Setup Instructions

This is a comprehensive document signing system built with Next.js, Supabase, and Google Services integration.

## Features

- **User Authentication**: Google OAuth integration with role-based access
- **Document Upload**: Secure file upload with metadata (subject, details, urgency)
- **Admin Dashboard**: Review, sign, and provide feedback on documents
- **User Dashboard**: Track document status and download signed documents
- **Google Drive Integration**: Automatic file storage and organization
- **Google Sheets Integration**: Automated tracking and reporting
- **Real-time Updates**: Live status updates and notifications

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Google Cloud Console** account
3. **Supabase** account
4. **Vercel** account (for deployment)

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd sign
npm install
```

### 2. Supabase Setup

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your URL and keys
3. Go to SQL Editor and run the `database-schema.sql` file
4. Update the admin email in the SQL file before running it

### 3. Google Cloud Console Setup

#### 3.1 Create a New Project
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select an existing one

#### 3.2 Enable APIs
Enable the following APIs:
- Google Drive API
- Google Sheets API
- Google OAuth2 API

#### 3.3 Create OAuth2 Credentials
1. Go to APIs & Services > Credentials
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Set application type to "Web application"
4. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://your-domain.vercel.app/api/auth/callback/google` (production)

#### 3.4 Create Service Account
1. Go to APIs & Services > Credentials
2. Click "Create Credentials" > "Service Account"
3. Download the JSON key file
4. Extract the `client_email` and `private_key` from the JSON

### 4. Google Drive Setup

1. Create a folder in Google Drive for document storage
2. Right-click the folder > Share > Add the service account email with Editor access
3. Copy the folder ID from the URL (the long string after `/folders/`)

### 5. Google Sheets Setup

1. Create a new Google Sheet for document tracking
2. Share it with the service account email (Editor access)
3. Copy the sheet ID from the URL (the long string between `/d/` and `/edit`)
4. Go to Extensions > Apps Script
5. Replace the default code with the content from `google-apps-script.js`
6. Update the configuration variables in the script
7. Run the `setup()` function once to initialize

### 6. Environment Variables

Create a `.env.local` file in the root directory:

```env
# Database
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_generate_a_random_string

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Google Services
GOOGLE_SERVICE_ACCOUNT_EMAIL=your_service_account_email
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id
GOOGLE_SHEETS_ID=your_google_sheets_id
```

### 7. Development

```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

### 8. Deployment to Vercel

1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Add all environment variables in Vercel dashboard
4. Update `NEXTAUTH_URL` to your Vercel domain
5. Update Google OAuth redirect URIs to include your Vercel domain

## Usage

### For Users
1. Sign in with Google account
2. Upload documents with subject, details, and urgency level
3. Track document status in the dashboard
4. Download signed documents when ready

### For Admins
1. Sign in with Google account (must be set as admin in database)
2. Review all uploaded documents
3. Provide feedback or sign documents
4. Upload signed versions back to users

## File Structure

```
src/
├── app/
│   ├── api/
│   │   ├── auth/
│   │   └── documents/
│   ├── auth/
│   ├── dashboard/
│   └── admin/
├── components/
│   ├── ui/
│   └── upload-modal.tsx
├── lib/
│   ├── auth.ts
│   ├── google.ts
│   ├── supabase.ts
│   └── utils.ts
└── types/
    └── database.ts
```

## Security Features

- Row Level Security (RLS) in Supabase
- Role-based access control
- Secure file upload with type validation
- Protected API routes
- Middleware for route protection

## Troubleshooting

### Common Issues

1. **Google API Errors**: Ensure all APIs are enabled and credentials are correct
2. **Supabase Connection**: Check URL and keys in environment variables
3. **File Upload Issues**: Verify Google Drive folder permissions
4. **Authentication Issues**: Check OAuth redirect URIs

### Support

For issues and questions, please check the documentation or create an issue in the repository.
