import { google } from 'googleapis'

// Check if Google credentials are available
const hasGoogleCredentials =
  process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL &&
  process.env.GOOGLE_PRIVATE_KEY &&
  process.env.GOOGLE_DRIVE_FOLDER_ID

let auth: any = null
let drive: any = null
let sheets: any = null

if (hasGoogleCredentials) {
  try {
    // Initialize Google Auth
    auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: [
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/spreadsheets',
      ],
    })

    drive = google.drive({ version: 'v3', auth })
    sheets = google.sheets({ version: 'v4', auth })
  } catch (error) {
    console.error('Failed to initialize Google services:', error)
  }
}

export { drive, sheets }
export const DRIVE_FOLDER_ID = process.env.GOOGLE_DRIVE_FOLDER_ID
export const SHEETS_ID = process.env.GOOGLE_SHEETS_ID

// Helper function to upload file to Google Drive
export async function uploadToGoogleDrive(
  fileName: string,
  fileBuffer: Buffer,
  mimeType: string,
  folderId?: string
) {
  if (!drive || !DRIVE_FOLDER_ID) {
    console.warn('Google Drive not configured, skipping file upload')
    // Return a mock response for development
    return {
      fileId: 'mock-file-id',
      webViewLink: '#',
      webContentLink: '#',
    }
  }

  try {
    const response = await drive.files.create({
      requestBody: {
        name: fileName,
        parents: folderId ? [folderId] : [DRIVE_FOLDER_ID],
      },
      media: {
        mimeType,
        body: fileBuffer,
      },
    })

    // Make file publicly viewable
    await drive.permissions.create({
      fileId: response.data.id!,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    })

    return {
      fileId: response.data.id!,
      webViewLink: `https://drive.google.com/file/d/${response.data.id}/view`,
      webContentLink: `https://drive.google.com/uc?id=${response.data.id}`,
    }
  } catch (error) {
    console.error('Error uploading to Google Drive:', error)
    throw error
  }
}

// Helper function to update Google Sheets
export async function updateGoogleSheet(values: any[][]) {
  if (!sheets || !SHEETS_ID) {
    console.warn('Google Sheets not configured, skipping sheet update')
    return
  }

  try {
    await sheets.spreadsheets.values.append({
      spreadsheetId: SHEETS_ID,
      range: 'Sheet1!A:H', // Adjust range as needed
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values,
      },
    })
  } catch (error) {
    console.error('Error updating Google Sheets:', error)
    // Don't throw error for sheets update failure
    console.warn('Continuing without sheet update')
  }
}
