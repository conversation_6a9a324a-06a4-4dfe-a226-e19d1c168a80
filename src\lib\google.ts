import { google } from 'googleapis'

// Initialize Google Auth
const auth = new google.auth.GoogleAuth({
  credentials: {
    client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  },
  scopes: [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/spreadsheets',
  ],
})

export const drive = google.drive({ version: 'v3', auth })
export const sheets = google.sheets({ version: 'v4', auth })

export const DRIVE_FOLDER_ID = process.env.GOOGLE_DRIVE_FOLDER_ID!
export const SHEETS_ID = process.env.GOOGLE_SHEETS_ID!

// Helper function to upload file to Google Drive
export async function uploadToGoogleDrive(
  fileName: string,
  fileBuffer: Buffer,
  mimeType: string,
  folderId?: string
) {
  try {
    const response = await drive.files.create({
      requestBody: {
        name: fileName,
        parents: folderId ? [folderId] : [DRIVE_FOLDER_ID],
      },
      media: {
        mimeType,
        body: fileBuffer,
      },
    })

    // Make file publicly viewable
    await drive.permissions.create({
      fileId: response.data.id!,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    })

    return {
      fileId: response.data.id!,
      webViewLink: `https://drive.google.com/file/d/${response.data.id}/view`,
      webContentLink: `https://drive.google.com/uc?id=${response.data.id}`,
    }
  } catch (error) {
    console.error('Error uploading to Google Drive:', error)
    throw error
  }
}

// Helper function to update Google Sheets
export async function updateGoogleSheet(values: any[][]) {
  try {
    await sheets.spreadsheets.values.append({
      spreadsheetId: SHEETS_ID,
      range: 'Sheet1!A:H', // Adjust range as needed
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values,
      },
    })
  } catch (error) {
    console.error('Error updating Google Sheets:', error)
    throw error
  }
}
