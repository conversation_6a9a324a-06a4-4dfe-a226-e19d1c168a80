# Testing Guide

This guide will help you test all features of the Document Signing System.

## Prerequisites

1. Complete the setup instructions in `SETUP.md`
2. Have at least one admin user configured in the database
3. Have test documents ready (PDF, DOC, or image files)

## Test Scenarios

### 1. Authentication Testing

#### Test User Registration/Login
1. Visit `http://localhost:3000`
2. Should redirect to `/auth/signin`
3. Click "Continue with Google"
4. Complete Google OAuth flow
5. Should redirect to `/dashboard` for regular users
6. Should redirect to `/admin` for admin users

#### Test Role-Based Access
1. Try accessing `/admin` as a regular user
2. Should redirect to `/dashboard`
3. Try accessing `/dashboard` as an admin
4. Should redirect to `/admin`

### 2. User Dashboard Testing

#### Test Document Upload
1. Login as a regular user
2. Click "Upload New Document"
3. Fill in the form:
   - Select a test file (PDF, DOC, or image)
   - Enter subject: "Test Contract Review"
   - Enter details: "Please review and sign this test contract"
   - Set urgency: "Urgent"
4. Click "Upload Document"
5. Verify document appears in the dashboard
6. Check Google Drive folder for the uploaded file
7. Check Google Sheets for the new entry

#### Test Document Status Tracking
1. Upload multiple documents with different urgency levels
2. Verify status badges display correctly
3. Check that stats cards update properly
4. Test filtering by clicking on different status cards

#### Test File Validation
1. Try uploading a file larger than 10MB
2. Try uploading an unsupported file type
3. Verify error messages display correctly

### 3. Admin Dashboard Testing

#### Test Document Review
1. Login as an admin user
2. Verify all uploaded documents are visible
3. Test filtering by status and urgency
4. Click "View" to open documents in Google Drive
5. Verify document details and user information

#### Test Document Signing
1. Select a pending document
2. Click "Sign" button
3. Upload a signed version of the document
4. Verify document status changes to "Signed"
5. Check that user can now see and download the signed document
6. Verify Google Sheets is updated with signed document link

#### Test Feedback System
1. Select a pending document
2. Click "Feedback" button
3. Enter feedback: "Please provide additional identification documents"
4. Submit feedback
5. Verify document status changes to "Feedback"
6. Login as the user and verify feedback is visible

### 4. Google Integration Testing

#### Test Google Drive Integration
1. Upload a document as a user
2. Check Google Drive folder:
   - Original document should be stored
   - File should be publicly viewable
   - File name should be preserved
3. Sign a document as admin
4. Check that signed document is also stored in Drive

#### Test Google Sheets Integration
1. Upload a document
2. Check Google Sheets:
   - New row should be added
   - All metadata should be populated
   - Original document link should work
3. Sign the document
4. Check that the same row is updated with:
   - Signed document link
   - Admin name
   - Signed date
   - Status change

#### Test Google Apps Script
1. Open the Google Sheet
2. Go to Extensions > Apps Script
3. Run the `getDocumentStats()` function
4. Verify it returns correct statistics
5. Test the `checkUrgentDocuments()` function
6. Verify urgent document notifications work

### 5. Security Testing

#### Test Authentication Protection
1. Try accessing protected routes without authentication:
   - `/dashboard`
   - `/admin`
   - `/api/documents`
2. Should redirect to signin or return 401

#### Test Role-Based Authorization
1. Try accessing admin endpoints as a regular user
2. Try viewing other users' documents
3. Verify proper access control

#### Test File Upload Security
1. Try uploading malicious file types
2. Test file size limits
3. Verify file type validation

### 6. UI/UX Testing

#### Test Responsive Design
1. Test on different screen sizes
2. Verify mobile responsiveness
3. Check that modals work properly on mobile

#### Test User Experience
1. Verify loading states work correctly
2. Test error handling and user feedback
3. Check that success messages appear
4. Verify navigation flows

### 7. Performance Testing

#### Test File Upload Performance
1. Upload various file sizes
2. Monitor upload progress
3. Test concurrent uploads

#### Test Dashboard Loading
1. Create multiple documents
2. Test dashboard loading with many documents
3. Verify pagination or performance optimizations

### 8. Error Handling Testing

#### Test Network Errors
1. Disconnect internet during upload
2. Verify error handling
3. Test retry mechanisms

#### Test API Errors
1. Temporarily break Google API credentials
2. Test error messages and fallbacks
3. Verify graceful degradation

## Automated Testing

### Unit Tests (Future Enhancement)
```bash
npm run test
```

### Type Checking
```bash
npm run type-check
```

### Linting
```bash
npm run lint
```

## Test Data Cleanup

After testing, clean up test data:

1. **Database**: Remove test documents from Supabase
2. **Google Drive**: Delete test files from the folder
3. **Google Sheets**: Clear test entries

## Common Issues and Solutions

### Upload Failures
- Check Google Drive API quotas
- Verify service account permissions
- Check file size and type restrictions

### Authentication Issues
- Verify Google OAuth configuration
- Check redirect URIs
- Ensure NextAuth secret is set

### Google Sheets Not Updating
- Check service account permissions
- Verify Apps Script triggers are set up
- Check API quotas

## Test Checklist

- [ ] User can register/login with Google
- [ ] Role-based access control works
- [ ] Document upload works with validation
- [ ] Files are stored in Google Drive
- [ ] Google Sheets is updated automatically
- [ ] Admin can review and sign documents
- [ ] Admin can provide feedback
- [ ] Users can download signed documents
- [ ] Status tracking works correctly
- [ ] Filters and search work
- [ ] Mobile responsiveness
- [ ] Error handling works
- [ ] Security measures are effective

## Reporting Issues

When reporting issues, include:
1. Steps to reproduce
2. Expected vs actual behavior
3. Browser and device information
4. Console errors (if any)
5. Screenshots (if applicable)
