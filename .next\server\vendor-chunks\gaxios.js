"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gaxios";
exports.ids = ["vendor-chunks/gaxios"];
exports.modules = {

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/common.js":
/*!*****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/common.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosError = exports.GAXIOS_ERROR_SYMBOL = void 0;\nexports.defaultErrorRedactor = defaultErrorRedactor;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\nconst util_cjs_1 = __importDefault(__webpack_require__(/*! ./util.cjs */ \"(rsc)/./node_modules/gaxios/build/cjs/src/util.cjs\"));\nconst pkg = util_cjs_1.default.pkg;\n/**\n * Support `instanceof` operator for `GaxiosError`s in different versions of this library.\n *\n * @see {@link GaxiosError[Symbol.hasInstance]}\n */\nexports.GAXIOS_ERROR_SYMBOL = Symbol.for(`${pkg.name}-gaxios-error`);\nclass GaxiosError extends Error {\n    config;\n    response;\n    /**\n     * An error code.\n     * Can be a system error code, DOMException error name, or any error's 'code' property where it is a `string`.\n     *\n     * It is only a `number` when the cause is sourced from an API-level error (AIP-193).\n     *\n     * @see {@link https://nodejs.org/api/errors.html#errorcode error.code}\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/DOMException#error_names DOMException#error_names}\n     * @see {@link https://google.aip.dev/193#http11json-representation AIP-193}\n     *\n     * @example\n     * 'ECONNRESET'\n     *\n     * @example\n     * 'TimeoutError'\n     *\n     * @example\n     * 500\n     */\n    code;\n    /**\n     * An HTTP Status code.\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Response/status Response#status}\n     *\n     * @example\n     * 500\n     */\n    status;\n    /**\n     * @deprecated use {@link GaxiosError.cause} instead.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause Error#cause}\n     *\n     * @privateRemarks\n     *\n     * We will want to remove this property later as the modern `cause` property is better suited\n     * for displaying and relaying nested errors. Keeping this here makes the resulting\n     * error log larger than it needs to be.\n     *\n     */\n    error;\n    /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[Symbol.hasInstance]}\n     * @see {@link https://github.com/microsoft/TypeScript/issues/13965#issuecomment-278570200}\n     * @see {@link https://stackoverflow.com/questions/46618852/require-and-instanceof}\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/@@hasInstance#reverting_to_default_instanceof_behavior}\n     */\n    [exports.GAXIOS_ERROR_SYMBOL] = pkg.version;\n    /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[GAXIOS_ERROR_SYMBOL]}\n     */\n    static [Symbol.hasInstance](instance) {\n        if (instance &&\n            typeof instance === 'object' &&\n            exports.GAXIOS_ERROR_SYMBOL in instance &&\n            instance[exports.GAXIOS_ERROR_SYMBOL] === pkg.version) {\n            return true;\n        }\n        // fallback to native\n        return Function.prototype[Symbol.hasInstance].call(GaxiosError, instance);\n    }\n    constructor(message, config, response, cause) {\n        super(message, { cause });\n        this.config = config;\n        this.response = response;\n        this.error = cause instanceof Error ? cause : undefined;\n        // deep-copy config as we do not want to mutate\n        // the existing config for future retries/use\n        this.config = (0, extend_1.default)(true, {}, config);\n        if (this.response) {\n            this.response.config = (0, extend_1.default)(true, {}, this.response.config);\n        }\n        if (this.response) {\n            try {\n                this.response.data = translateData(this.config.responseType, \n                // workaround for `node-fetch`'s `.data` deprecation...\n                this.response?.bodyUsed ? this.response?.data : undefined);\n            }\n            catch {\n                // best effort - don't throw an error within an error\n                // we could set `this.response.config.responseType = 'unknown'`, but\n                // that would mutate future calls with this config object.\n            }\n            this.status = this.response.status;\n        }\n        if (cause instanceof DOMException) {\n            // The DOMException's equivalent to code is its name\n            // E.g.: name = `TimeoutError`, code = number\n            // https://developer.mozilla.org/en-US/docs/Web/API/DOMException/name\n            this.code = cause.name;\n        }\n        else if (cause &&\n            typeof cause === 'object' &&\n            'code' in cause &&\n            (typeof cause.code === 'string' || typeof cause.code === 'number')) {\n            this.code = cause.code;\n        }\n    }\n    /**\n     * An AIP-193 conforming error extractor.\n     *\n     * @see {@link https://google.aip.dev/193#http11json-representation AIP-193}\n     *\n     * @internal\n     * @expiremental\n     *\n     * @param res the response object\n     * @returns the extracted error information\n     */\n    static extractAPIErrorFromResponse(res, defaultErrorMessage = 'The request failed') {\n        let message = defaultErrorMessage;\n        // Use res.data as the error message\n        if (typeof res.data === 'string') {\n            message = res.data;\n        }\n        if (res.data &&\n            typeof res.data === 'object' &&\n            'error' in res.data &&\n            res.data.error &&\n            !res.ok) {\n            if (typeof res.data.error === 'string') {\n                return {\n                    message: res.data.error,\n                    code: res.status,\n                    status: res.statusText,\n                };\n            }\n            if (typeof res.data.error === 'object') {\n                // extract status from data.message\n                message =\n                    'message' in res.data.error &&\n                        typeof res.data.error.message === 'string'\n                        ? res.data.error.message\n                        : message;\n                // extract status from data.error\n                const status = 'status' in res.data.error &&\n                    typeof res.data.error.status === 'string'\n                    ? res.data.error.status\n                    : res.statusText;\n                // extract code from data.error\n                const code = 'code' in res.data.error && typeof res.data.error.code === 'number'\n                    ? res.data.error.code\n                    : res.status;\n                if ('errors' in res.data.error &&\n                    Array.isArray(res.data.error.errors)) {\n                    const errorMessages = [];\n                    for (const e of res.data.error.errors) {\n                        if (typeof e === 'object' &&\n                            'message' in e &&\n                            typeof e.message === 'string') {\n                            errorMessages.push(e.message);\n                        }\n                    }\n                    return Object.assign({\n                        message: errorMessages.join('\\n') || message,\n                        code,\n                        status,\n                    }, res.data.error);\n                }\n                return Object.assign({\n                    message,\n                    code,\n                    status,\n                }, res.data.error);\n            }\n        }\n        return {\n            message,\n            code: res.status,\n            status: res.statusText,\n        };\n    }\n}\nexports.GaxiosError = GaxiosError;\nfunction translateData(responseType, data) {\n    switch (responseType) {\n        case 'stream':\n            return data;\n        case 'json':\n            return JSON.parse(JSON.stringify(data));\n        case 'arraybuffer':\n            return JSON.parse(Buffer.from(data).toString('utf8'));\n        case 'blob':\n            return JSON.parse(data.text());\n        default:\n            return data;\n    }\n}\n/**\n * An experimental error redactor.\n *\n * @param config Config to potentially redact properties of\n * @param response Config to potentially redact properties of\n *\n * @experimental\n */\nfunction defaultErrorRedactor(data) {\n    const REDACT = '<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.';\n    function redactHeaders(headers) {\n        if (!headers)\n            return;\n        headers.forEach((_, key) => {\n            // any casing of `Authentication`\n            // any casing of `Authorization`\n            // anything containing secret, such as 'client secret'\n            if (/^authentication$/i.test(key) ||\n                /^authorization$/i.test(key) ||\n                /secret/i.test(key))\n                headers.set(key, REDACT);\n        });\n    }\n    function redactString(obj, key) {\n        if (typeof obj === 'object' &&\n            obj !== null &&\n            typeof obj[key] === 'string') {\n            const text = obj[key];\n            if (/grant_type=/i.test(text) ||\n                /assertion=/i.test(text) ||\n                /secret/i.test(text)) {\n                obj[key] = REDACT;\n            }\n        }\n    }\n    function redactObject(obj) {\n        if (!obj || typeof obj !== 'object') {\n            return;\n        }\n        else if (obj instanceof FormData ||\n            obj instanceof URLSearchParams ||\n            // support `node-fetch` FormData/URLSearchParams\n            ('forEach' in obj && 'set' in obj)) {\n            obj.forEach((_, key) => {\n                if (['grant_type', 'assertion'].includes(key) || /secret/.test(key)) {\n                    obj.set(key, REDACT);\n                }\n            });\n        }\n        else {\n            if ('grant_type' in obj) {\n                obj['grant_type'] = REDACT;\n            }\n            if ('assertion' in obj) {\n                obj['assertion'] = REDACT;\n            }\n            if ('client_secret' in obj) {\n                obj['client_secret'] = REDACT;\n            }\n        }\n    }\n    if (data.config) {\n        redactHeaders(data.config.headers);\n        redactString(data.config, 'data');\n        redactObject(data.config.data);\n        redactString(data.config, 'body');\n        redactObject(data.config.body);\n        if (data.config.url.searchParams.has('token')) {\n            data.config.url.searchParams.set('token', REDACT);\n        }\n        if (data.config.url.searchParams.has('client_secret')) {\n            data.config.url.searchParams.set('client_secret', REDACT);\n        }\n    }\n    if (data.response) {\n        defaultErrorRedactor({ config: data.response.config });\n        redactHeaders(data.response.headers);\n        // workaround for `node-fetch`'s `.data` deprecation...\n        if (data.response.bodyUsed) {\n            redactString(data.response, 'data');\n            redactObject(data.response.data);\n        }\n    }\n    return data;\n}\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/gaxios.js":
/*!*****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/gaxios.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Gaxios = void 0;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst common_js_1 = __webpack_require__(/*! ./common.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/common.js\");\nconst retry_js_1 = __webpack_require__(/*! ./retry.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/retry.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst interceptor_js_1 = __webpack_require__(/*! ./interceptor.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js\");\nconst randomUUID = async () => globalThis.crypto?.randomUUID() || (await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! crypto */ \"crypto\", 23))).randomUUID();\nclass Gaxios {\n    agentCache = new Map();\n    /**\n     * Default HTTP options that will be used for every HTTP request.\n     */\n    defaults;\n    /**\n     * Interceptors\n     */\n    interceptors;\n    /**\n     * The Gaxios class is responsible for making HTTP requests.\n     * @param defaults The default set of options to be used for this instance.\n     */\n    constructor(defaults) {\n        this.defaults = defaults || {};\n        this.interceptors = {\n            request: new interceptor_js_1.GaxiosInterceptorManager(),\n            response: new interceptor_js_1.GaxiosInterceptorManager(),\n        };\n    }\n    /**\n     * A {@link fetch `fetch`} compliant API for {@link Gaxios}.\n     *\n     * @remarks\n     *\n     * This is useful as a drop-in replacement for `fetch` API usage.\n     *\n     * @example\n     *\n     * ```ts\n     * const gaxios = new Gaxios();\n     * const myFetch: typeof fetch = (...args) => gaxios.fetch(...args);\n     * await myFetch('https://example.com');\n     * ```\n     *\n     * @param args `fetch` API or `Gaxios#request` parameters\n     * @returns the {@link Response} with Gaxios-added properties\n     */\n    fetch(...args) {\n        // Up to 2 parameters in either overload\n        const input = args[0];\n        const init = args[1];\n        let url = undefined;\n        const headers = new Headers();\n        // prepare URL\n        if (typeof input === 'string') {\n            url = new URL(input);\n        }\n        else if (input instanceof URL) {\n            url = input;\n        }\n        else if (input && input.url) {\n            url = new URL(input.url);\n        }\n        // prepare headers\n        if (input && typeof input === 'object' && 'headers' in input) {\n            _a.mergeHeaders(headers, input.headers);\n        }\n        if (init) {\n            _a.mergeHeaders(headers, new Headers(init.headers));\n        }\n        // prepare request\n        if (typeof input === 'object' && !(input instanceof URL)) {\n            // input must have been a non-URL object\n            return this.request({ ...init, ...input, headers, url });\n        }\n        else {\n            // input must have been a string or URL\n            return this.request({ ...init, headers, url });\n        }\n    }\n    /**\n     * Perform an HTTP request with the given options.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async request(opts = {}) {\n        let prepared = await this.#prepareRequest(opts);\n        prepared = await this.#applyRequestInterceptors(prepared);\n        return this.#applyResponseInterceptors(this._request(prepared));\n    }\n    async _defaultAdapter(config) {\n        const fetchImpl = config.fetchImplementation ||\n            this.defaults.fetchImplementation ||\n            (await _a.#getFetch());\n        // node-fetch v3 warns when `data` is present\n        // https://github.com/node-fetch/node-fetch/issues/1000\n        const preparedOpts = { ...config };\n        delete preparedOpts.data;\n        const res = (await fetchImpl(config.url, preparedOpts));\n        const data = await this.getResponseData(config, res);\n        if (!Object.getOwnPropertyDescriptor(res, 'data')?.configurable) {\n            // Work-around for `node-fetch` v3 as accessing `data` would otherwise throw\n            Object.defineProperties(res, {\n                data: {\n                    configurable: true,\n                    writable: true,\n                    enumerable: true,\n                    value: data,\n                },\n            });\n        }\n        // Keep object as an instance of `Response`\n        return Object.assign(res, { config, data });\n    }\n    /**\n     * Internal, retryable version of the `request` method.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async _request(opts) {\n        try {\n            let translatedResponse;\n            if (opts.adapter) {\n                translatedResponse = await opts.adapter(opts, this._defaultAdapter.bind(this));\n            }\n            else {\n                translatedResponse = await this._defaultAdapter(opts);\n            }\n            if (!opts.validateStatus(translatedResponse.status)) {\n                if (opts.responseType === 'stream') {\n                    const response = [];\n                    for await (const chunk of (opts.data ?? [])) {\n                        response.push(chunk);\n                    }\n                    translatedResponse.data = response;\n                }\n                const errorInfo = common_js_1.GaxiosError.extractAPIErrorFromResponse(translatedResponse, `Request failed with status code ${translatedResponse.status}`);\n                throw new common_js_1.GaxiosError(errorInfo?.message, opts, translatedResponse, errorInfo);\n            }\n            return translatedResponse;\n        }\n        catch (e) {\n            let err;\n            if (e instanceof common_js_1.GaxiosError) {\n                err = e;\n            }\n            else if (e instanceof Error) {\n                err = new common_js_1.GaxiosError(e.message, opts, undefined, e);\n            }\n            else {\n                err = new common_js_1.GaxiosError('Unexpected Gaxios Error', opts, undefined, e);\n            }\n            const { shouldRetry, config } = await (0, retry_js_1.getRetryConfig)(err);\n            if (shouldRetry && config) {\n                err.config.retryConfig.currentRetryAttempt =\n                    config.retryConfig.currentRetryAttempt;\n                // The error's config could be redacted - therefore we only want to\n                // copy the retry state over to the existing config\n                opts.retryConfig = err.config?.retryConfig;\n                // re-prepare timeout for the next request\n                this.#appendTimeoutToSignal(opts);\n                return this._request(opts);\n            }\n            if (opts.errorRedactor) {\n                opts.errorRedactor(err);\n            }\n            throw err;\n        }\n    }\n    async getResponseData(opts, res) {\n        if (opts.maxContentLength &&\n            res.headers.has('content-length') &&\n            opts.maxContentLength <\n                Number.parseInt(res.headers?.get('content-length') || '')) {\n            throw new common_js_1.GaxiosError(\"Response's `Content-Length` is over the limit.\", opts, Object.assign(res, { config: opts }));\n        }\n        switch (opts.responseType) {\n            case 'stream':\n                return res.body;\n            case 'json':\n                return res.json();\n            case 'arraybuffer':\n                return res.arrayBuffer();\n            case 'blob':\n                return res.blob();\n            case 'text':\n                return res.text();\n            default:\n                return this.getResponseDataFromContentType(res);\n        }\n    }\n    #urlMayUseProxy(url, noProxy = []) {\n        const candidate = new URL(url);\n        const noProxyList = [...noProxy];\n        const noProxyEnvList = (process.env.NO_PROXY ?? process.env.no_proxy)?.split(',') || [];\n        for (const rule of noProxyEnvList) {\n            noProxyList.push(rule.trim());\n        }\n        for (const rule of noProxyList) {\n            // Match regex\n            if (rule instanceof RegExp) {\n                if (rule.test(candidate.toString())) {\n                    return false;\n                }\n            }\n            // Match URL\n            else if (rule instanceof URL) {\n                if (rule.origin === candidate.origin) {\n                    return false;\n                }\n            }\n            // Match string regex\n            else if (rule.startsWith('*.') || rule.startsWith('.')) {\n                const cleanedRule = rule.replace(/^\\*\\./, '.');\n                if (candidate.hostname.endsWith(cleanedRule)) {\n                    return false;\n                }\n            }\n            // Basic string match\n            else if (rule === candidate.origin ||\n                rule === candidate.hostname ||\n                rule === candidate.href) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Applies the request interceptors. The request interceptors are applied after the\n     * call to prepareRequest is completed.\n     *\n     * @param {GaxiosOptionsPrepared} options The current set of options.\n     *\n     * @returns {Promise<GaxiosOptionsPrepared>} Promise that resolves to the set of options or response after interceptors are applied.\n     */\n    async #applyRequestInterceptors(options) {\n        let promiseChain = Promise.resolve(options);\n        for (const interceptor of this.interceptors.request.values()) {\n            if (interceptor) {\n                promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n            }\n        }\n        return promiseChain;\n    }\n    /**\n     * Applies the response interceptors. The response interceptors are applied after the\n     * call to request is made.\n     *\n     * @param {GaxiosOptionsPrepared} options The current set of options.\n     *\n     * @returns {Promise<GaxiosOptionsPrepared>} Promise that resolves to the set of options or response after interceptors are applied.\n     */\n    async #applyResponseInterceptors(response) {\n        let promiseChain = Promise.resolve(response);\n        for (const interceptor of this.interceptors.response.values()) {\n            if (interceptor) {\n                promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n            }\n        }\n        return promiseChain;\n    }\n    /**\n     * Validates the options, merges them with defaults, and prepare request.\n     *\n     * @param options The original options passed from the client.\n     * @returns Prepared options, ready to make a request\n     */\n    async #prepareRequest(options) {\n        // Prepare Headers - copy in order to not mutate the original objects\n        const preparedHeaders = new Headers(this.defaults.headers);\n        _a.mergeHeaders(preparedHeaders, options.headers);\n        // Merge options\n        const opts = (0, extend_1.default)(true, {}, this.defaults, options);\n        if (!opts.url) {\n            throw new Error('URL is required.');\n        }\n        if (opts.baseURL) {\n            opts.url = new URL(opts.url, opts.baseURL);\n        }\n        // don't modify the properties of a default or provided URL\n        opts.url = new URL(opts.url);\n        if (opts.params) {\n            if (opts.paramsSerializer) {\n                let additionalQueryParams = opts.paramsSerializer(opts.params);\n                if (additionalQueryParams.startsWith('?')) {\n                    additionalQueryParams = additionalQueryParams.slice(1);\n                }\n                const prefix = opts.url.toString().includes('?') ? '&' : '?';\n                opts.url = opts.url + prefix + additionalQueryParams;\n            }\n            else {\n                const url = opts.url instanceof URL ? opts.url : new URL(opts.url);\n                for (const [key, value] of new URLSearchParams(opts.params)) {\n                    url.searchParams.append(key, value);\n                }\n                opts.url = url;\n            }\n        }\n        if (typeof options.maxContentLength === 'number') {\n            opts.size = options.maxContentLength;\n        }\n        if (typeof options.maxRedirects === 'number') {\n            opts.follow = options.maxRedirects;\n        }\n        const shouldDirectlyPassData = typeof opts.data === 'string' ||\n            opts.data instanceof ArrayBuffer ||\n            opts.data instanceof Blob ||\n            // Node 18 does not have a global `File` object\n            (globalThis.File && opts.data instanceof File) ||\n            opts.data instanceof FormData ||\n            opts.data instanceof stream_1.Readable ||\n            opts.data instanceof ReadableStream ||\n            opts.data instanceof String ||\n            opts.data instanceof URLSearchParams ||\n            ArrayBuffer.isView(opts.data) || // `Buffer` (Node.js), `DataView`, `TypedArray`\n            /**\n             * @deprecated `node-fetch` or another third-party's request types\n             */\n            ['Blob', 'File', 'FormData'].includes(opts.data?.constructor?.name || '');\n        if (opts.multipart?.length) {\n            const boundary = await randomUUID();\n            preparedHeaders.set('content-type', `multipart/related; boundary=${boundary}`);\n            opts.body = stream_1.Readable.from(this.getMultipartRequest(opts.multipart, boundary));\n        }\n        else if (shouldDirectlyPassData) {\n            opts.body = opts.data;\n        }\n        else if (typeof opts.data === 'object') {\n            if (preparedHeaders.get('Content-Type') ===\n                'application/x-www-form-urlencoded') {\n                // If www-form-urlencoded content type has been set, but data is\n                // provided as an object, serialize the content\n                opts.body = opts.paramsSerializer\n                    ? opts.paramsSerializer(opts.data)\n                    : new URLSearchParams(opts.data);\n            }\n            else {\n                if (!preparedHeaders.has('content-type')) {\n                    preparedHeaders.set('content-type', 'application/json');\n                }\n                opts.body = JSON.stringify(opts.data);\n            }\n        }\n        else if (opts.data) {\n            opts.body = opts.data;\n        }\n        opts.validateStatus = opts.validateStatus || this.validateStatus;\n        opts.responseType = opts.responseType || 'unknown';\n        if (!preparedHeaders.has('accept') && opts.responseType === 'json') {\n            preparedHeaders.set('accept', 'application/json');\n        }\n        const proxy = opts.proxy ||\n            process?.env?.HTTPS_PROXY ||\n            process?.env?.https_proxy ||\n            process?.env?.HTTP_PROXY ||\n            process?.env?.http_proxy;\n        if (opts.agent) {\n            // don't do any of the following options - use the user-provided agent.\n        }\n        else if (proxy && this.#urlMayUseProxy(opts.url, opts.noProxy)) {\n            const HttpsProxyAgent = await _a.#getProxyAgent();\n            if (this.agentCache.has(proxy)) {\n                opts.agent = this.agentCache.get(proxy);\n            }\n            else {\n                opts.agent = new HttpsProxyAgent(proxy, {\n                    cert: opts.cert,\n                    key: opts.key,\n                });\n                this.agentCache.set(proxy, opts.agent);\n            }\n        }\n        else if (opts.cert && opts.key) {\n            // Configure client for mTLS\n            if (this.agentCache.has(opts.key)) {\n                opts.agent = this.agentCache.get(opts.key);\n            }\n            else {\n                opts.agent = new https_1.Agent({\n                    cert: opts.cert,\n                    key: opts.key,\n                });\n                this.agentCache.set(opts.key, opts.agent);\n            }\n        }\n        if (typeof opts.errorRedactor !== 'function' &&\n            opts.errorRedactor !== false) {\n            opts.errorRedactor = common_js_1.defaultErrorRedactor;\n        }\n        if (opts.body && !('duplex' in opts)) {\n            /**\n             * required for Node.js and the type isn't available today\n             * @link https://github.com/nodejs/node/issues/46221\n             * @link https://github.com/microsoft/TypeScript-DOM-lib-generator/issues/1483\n             */\n            opts.duplex = 'half';\n        }\n        this.#appendTimeoutToSignal(opts);\n        return Object.assign(opts, {\n            headers: preparedHeaders,\n            url: opts.url instanceof URL ? opts.url : new URL(opts.url),\n        });\n    }\n    #appendTimeoutToSignal(opts) {\n        if (opts.timeout) {\n            const timeoutSignal = AbortSignal.timeout(opts.timeout);\n            if (opts.signal && !opts.signal.aborted) {\n                opts.signal = AbortSignal.any([opts.signal, timeoutSignal]);\n            }\n            else {\n                opts.signal = timeoutSignal;\n            }\n        }\n    }\n    /**\n     * By default, throw for any non-2xx status code\n     * @param status status code from the HTTP response\n     */\n    validateStatus(status) {\n        return status >= 200 && status < 300;\n    }\n    /**\n     * Attempts to parse a response by looking at the Content-Type header.\n     * @param {Response} response the HTTP response.\n     * @returns a promise that resolves to the response data.\n     */\n    async getResponseDataFromContentType(response) {\n        let contentType = response.headers.get('Content-Type');\n        if (contentType === null) {\n            // Maintain existing functionality by calling text()\n            return response.text();\n        }\n        contentType = contentType.toLowerCase();\n        if (contentType.includes('application/json')) {\n            let data = await response.text();\n            try {\n                data = JSON.parse(data);\n            }\n            catch {\n                // continue\n            }\n            return data;\n        }\n        else if (contentType.match(/^text\\//)) {\n            return response.text();\n        }\n        else {\n            // If the content type is something not easily handled, just return the raw data (blob)\n            return response.blob();\n        }\n    }\n    /**\n     * Creates an async generator that yields the pieces of a multipart/related request body.\n     * This implementation follows the spec: https://www.ietf.org/rfc/rfc2387.txt. However, recursive\n     * multipart/related requests are not currently supported.\n     *\n     * @param {GaxioMultipartOptions[]} multipartOptions the pieces to turn into a multipart/related body.\n     * @param {string} boundary the boundary string to be placed between each part.\n     */\n    async *getMultipartRequest(multipartOptions, boundary) {\n        const finale = `--${boundary}--`;\n        for (const currentPart of multipartOptions) {\n            const partContentType = currentPart.headers.get('Content-Type') || 'application/octet-stream';\n            const preamble = `--${boundary}\\r\\nContent-Type: ${partContentType}\\r\\n\\r\\n`;\n            yield preamble;\n            if (typeof currentPart.content === 'string') {\n                yield currentPart.content;\n            }\n            else {\n                yield* currentPart.content;\n            }\n            yield '\\r\\n';\n        }\n        yield finale;\n    }\n    /**\n     * A cache for the lazily-loaded proxy agent.\n     *\n     * Should use {@link Gaxios[#getProxyAgent]} to retrieve.\n     */\n    // using `import` to dynamically import the types here\n    static #proxyAgent;\n    /**\n     * A cache for the lazily-loaded fetch library.\n     *\n     * Should use {@link Gaxios[#getFetch]} to retrieve.\n     */\n    //\n    static #fetch;\n    /**\n     * Imports, caches, and returns a proxy agent - if not already imported\n     *\n     * @returns A proxy agent\n     */\n    static async #getProxyAgent() {\n        this.#proxyAgent ||= (await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/https-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/agent-base\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/has-flag\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! https-proxy-agent */ \"(rsc)/./node_modules/https-proxy-agent/dist/index.js\", 23))).HttpsProxyAgent;\n        return this.#proxyAgent;\n    }\n    static async #getFetch() {\n        const hasWindow = typeof window !== 'undefined' && !!window;\n        this.#fetch ||= hasWindow\n            ? window.fetch\n            : (await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/fetch-blob\"), __webpack_require__.e(\"vendor-chunks/formdata-polyfill\"), __webpack_require__.e(\"vendor-chunks/data-uri-to-buffer\"), __webpack_require__.e(\"vendor-chunks/web-streams-polyfill\"), __webpack_require__.e(\"vendor-chunks/node-domexception\")]).then(__webpack_require__.bind(__webpack_require__, /*! node-fetch */ \"(rsc)/./node_modules/node-fetch/src/index.js\"))).default;\n        return this.#fetch;\n    }\n    /**\n     * Merges headers.\n     * If the base headers do not exist a new `Headers` object will be returned.\n     *\n     * @remarks\n     *\n     * Using this utility can be helpful when the headers are not known to exist:\n     * - if they exist as `Headers`, that instance will be used\n     *   - it improves performance and allows users to use their existing references to their `Headers`\n     * - if they exist in another form (`HeadersInit`), they will be used to create a new `Headers` object\n     * - if the base headers do not exist a new `Headers` object will be created\n     *\n     * @param base headers to append/overwrite to\n     * @param append headers to append/overwrite with\n     * @returns the base headers instance with merged `Headers`\n     */\n    static mergeHeaders(base, ...append) {\n        base = base instanceof Headers ? base : new Headers(base);\n        for (const headers of append) {\n            const add = headers instanceof Headers ? headers : new Headers(headers);\n            add.forEach((value, key) => {\n                // set-cookie is the only header that would repeat.\n                // A bit of background: https://developer.mozilla.org/en-US/docs/Web/API/Headers/getSetCookie\n                key === 'set-cookie' ? base.append(key, value) : base.set(key, value);\n            });\n        }\n        return base;\n    }\n}\nexports.Gaxios = Gaxios;\n_a = Gaxios;\n//# sourceMappingURL=gaxios.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/gaxios.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/index.js":
/*!****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.instance = exports.Gaxios = exports.GaxiosError = void 0;\nexports.request = request;\nconst gaxios_js_1 = __webpack_require__(/*! ./gaxios.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/gaxios.js\");\nObject.defineProperty(exports, \"Gaxios\", ({ enumerable: true, get: function () { return gaxios_js_1.Gaxios; } }));\nvar common_js_1 = __webpack_require__(/*! ./common.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/common.js\");\nObject.defineProperty(exports, \"GaxiosError\", ({ enumerable: true, get: function () { return common_js_1.GaxiosError; } }));\n__exportStar(__webpack_require__(/*! ./interceptor.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js\"), exports);\n/**\n * The default instance used when the `request` method is directly\n * invoked.\n */\nexports.instance = new gaxios_js_1.Gaxios();\n/**\n * Make an HTTP request using the given options.\n * @param opts Options for the request\n */\nasync function request(opts) {\n    return exports.instance.request(opts);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js":
/*!**********************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/interceptor.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosInterceptorManager = void 0;\n/**\n * Class to manage collections of GaxiosInterceptors for both requests and responses.\n */\nclass GaxiosInterceptorManager extends Set {\n}\nexports.GaxiosInterceptorManager = GaxiosInterceptorManager;\n//# sourceMappingURL=interceptor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL2Nqcy9zcmMvaW50ZXJjZXB0b3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcZ2F4aW9zXFxidWlsZFxcY2pzXFxzcmNcXGludGVyY2VwdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gQ29weXJpZ2h0IDIwMjQgR29vZ2xlIExMQ1xuLy8gTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbi8vIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbi8vIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuLy9cbi8vICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuLy9cbi8vIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbi8vIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbi8vIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuLy8gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuLy8gbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkdheGlvc0ludGVyY2VwdG9yTWFuYWdlciA9IHZvaWQgMDtcbi8qKlxuICogQ2xhc3MgdG8gbWFuYWdlIGNvbGxlY3Rpb25zIG9mIEdheGlvc0ludGVyY2VwdG9ycyBmb3IgYm90aCByZXF1ZXN0cyBhbmQgcmVzcG9uc2VzLlxuICovXG5jbGFzcyBHYXhpb3NJbnRlcmNlcHRvck1hbmFnZXIgZXh0ZW5kcyBTZXQge1xufVxuZXhwb3J0cy5HYXhpb3NJbnRlcmNlcHRvck1hbmFnZXIgPSBHYXhpb3NJbnRlcmNlcHRvck1hbmFnZXI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnRlcmNlcHRvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/retry.js":
/*!****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/retry.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRetryConfig = getRetryConfig;\nasync function getRetryConfig(err) {\n    let config = getConfig(err);\n    if (!err || !err.config || (!config && !err.config.retry)) {\n        return { shouldRetry: false };\n    }\n    config = config || {};\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    config.retry =\n        config.retry === undefined || config.retry === null ? 3 : config.retry;\n    config.httpMethodsToRetry = config.httpMethodsToRetry || [\n        'GET',\n        'HEAD',\n        'PUT',\n        'OPTIONS',\n        'DELETE',\n    ];\n    config.noResponseRetries =\n        config.noResponseRetries === undefined || config.noResponseRetries === null\n            ? 2\n            : config.noResponseRetries;\n    config.retryDelayMultiplier = config.retryDelayMultiplier\n        ? config.retryDelayMultiplier\n        : 2;\n    config.timeOfFirstRequest = config.timeOfFirstRequest\n        ? config.timeOfFirstRequest\n        : Date.now();\n    config.totalTimeout = config.totalTimeout\n        ? config.totalTimeout\n        : Number.MAX_SAFE_INTEGER;\n    config.maxRetryDelay = config.maxRetryDelay\n        ? config.maxRetryDelay\n        : Number.MAX_SAFE_INTEGER;\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    const retryRanges = [\n        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n        // 1xx - Retry (Informational, request still processing)\n        // 2xx - Do not retry (Success)\n        // 3xx - Do not retry (Redirect)\n        // 4xx - Do not retry (Client errors)\n        // 408 - Retry (\"Request Timeout\")\n        // 429 - Retry (\"Too Many Requests\")\n        // 5xx - Retry (Server errors)\n        [100, 199],\n        [408, 408],\n        [429, 429],\n        [500, 599],\n    ];\n    config.statusCodesToRetry = config.statusCodesToRetry || retryRanges;\n    // Put the config back into the err\n    err.config.retryConfig = config;\n    // Determine if we should retry the request\n    const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n    if (!(await shouldRetryFn(err))) {\n        return { shouldRetry: false, config: err.config };\n    }\n    const delay = getNextRetryDelay(config);\n    // We're going to retry!  Increment the counter.\n    err.config.retryConfig.currentRetryAttempt += 1;\n    // Create a promise that invokes the retry after the backOffDelay\n    const backoff = config.retryBackoff\n        ? config.retryBackoff(err, delay)\n        : new Promise(resolve => {\n            setTimeout(resolve, delay);\n        });\n    // Notify the user if they added an `onRetryAttempt` handler\n    if (config.onRetryAttempt) {\n        await config.onRetryAttempt(err);\n    }\n    // Return the promise in which recalls Gaxios to retry the request\n    await backoff;\n    return { shouldRetry: true, config: err.config };\n}\n/**\n * Determine based on config if we should retry the request.\n * @param err The GaxiosError passed to the interceptor.\n */\nfunction shouldRetryRequest(err) {\n    const config = getConfig(err);\n    if ((err.config.signal?.aborted && err.code !== 'TimeoutError') ||\n        err.code === 'AbortError') {\n        return false;\n    }\n    // If there's no config, or retries are disabled, return.\n    if (!config || config.retry === 0) {\n        return false;\n    }\n    // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n    if (!err.response &&\n        (config.currentRetryAttempt || 0) >= config.noResponseRetries) {\n        return false;\n    }\n    // Only retry with configured HttpMethods.\n    if (!config.httpMethodsToRetry ||\n        !config.httpMethodsToRetry.includes(err.config.method?.toUpperCase() || 'GET')) {\n        return false;\n    }\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    if (err.response && err.response.status) {\n        let isInRange = false;\n        for (const [min, max] of config.statusCodesToRetry) {\n            const status = err.response.status;\n            if (status >= min && status <= max) {\n                isInRange = true;\n                break;\n            }\n        }\n        if (!isInRange) {\n            return false;\n        }\n    }\n    // If we are out of retry attempts, return\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    if (config.currentRetryAttempt >= config.retry) {\n        return false;\n    }\n    return true;\n}\n/**\n * Acquire the raxConfig object from an GaxiosError if available.\n * @param err The Gaxios error with a config object.\n */\nfunction getConfig(err) {\n    if (err && err.config && err.config.retryConfig) {\n        return err.config.retryConfig;\n    }\n    return;\n}\n/**\n * Gets the delay to wait before the next retry.\n *\n * @param {RetryConfig} config The current set of retry options\n * @returns {number} the amount of ms to wait before the next retry attempt.\n */\nfunction getNextRetryDelay(config) {\n    // Calculate time to wait with exponential backoff.\n    // If this is the first retry, look for a configured retryDelay.\n    const retryDelay = config.currentRetryAttempt\n        ? 0\n        : (config.retryDelay ?? 100);\n    // Formula: retryDelay + ((retryDelayMultiplier^currentRetryAttempt - 1 / 2) * 1000)\n    const calculatedDelay = retryDelay +\n        ((Math.pow(config.retryDelayMultiplier, config.currentRetryAttempt) - 1) /\n            2) *\n            1000;\n    const maxAllowableDelay = config.totalTimeout - (Date.now() - config.timeOfFirstRequest);\n    return Math.min(calculatedDelay, maxAllowableDelay, config.maxRetryDelay);\n}\n//# sourceMappingURL=retry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/util.cjs":
/*!****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/util.cjs ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n// Copyright 2023 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nconst pkg = __webpack_require__(/*! ../../../package.json */ \"(rsc)/./node_modules/gaxios/package.json\");\nmodule.exports = { pkg };\n//# sourceMappingURL=util.cjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL2Nqcy9zcmMvdXRpbC5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLG1CQUFPLENBQUMsdUVBQXVCO0FBQzNDLG1CQUFtQjtBQUNuQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqYXlrZVxcRG9jdW1lbnRzXFxTb3VyY2UgQ29kZVxcc2lnblxcbm9kZV9tb2R1bGVzXFxnYXhpb3NcXGJ1aWxkXFxjanNcXHNyY1xcdXRpbC5janMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBDb3B5cmlnaHQgMjAyMyBHb29nbGUgTExDXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4vL1xuLy8gVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuLy8gZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuLy8gV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4vLyBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4vLyBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbmNvbnN0IHBrZyA9IHJlcXVpcmUoJy4uLy4uLy4uL3BhY2thZ2UuanNvbicpO1xubW9kdWxlLmV4cG9ydHMgPSB7IHBrZyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbC5janMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/util.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/package.json":
/*!******************************************!*\
  !*** ./node_modules/gaxios/package.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"gaxios","version":"7.1.1","description":"A simple common HTTP client specifically for Google APIs and services.","main":"build/cjs/src/index.js","types":"build/cjs/src/index.d.ts","files":["build/"],"exports":{".":{"import":{"types":"./build/esm/src/index.d.ts","default":"./build/esm/src/index.js"},"require":{"types":"./build/cjs/src/index.d.ts","default":"./build/cjs/src/index.js"}}},"scripts":{"lint":"gts check --no-inline-config","test":"c8 mocha build/esm/test","presystem-test":"npm run compile","system-test":"mocha build/esm/system-test --timeout 80000","compile":"tsc -b ./tsconfig.json ./tsconfig.cjs.json && node utils/enable-esm.mjs","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","webpack":"webpack","prebrowser-test":"npm run compile","browser-test":"node build/browser-test/browser-test-runner.js","docs":"jsdoc -c .jsdoc.js","docs-test":"linkinator docs","predocs-test":"npm run docs","samples-test":"cd samples/ && npm link ../ && npm test && cd ../","prelint":"cd samples; npm link ../; npm install","clean":"gts clean"},"repository":"googleapis/gaxios","keywords":["google"],"engines":{"node":">=18"},"author":"Google, LLC","license":"Apache-2.0","devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@types/cors":"^2.8.6","@types/express":"^5.0.0","@types/extend":"^3.0.1","@types/mocha":"^10.0.10","@types/multiparty":"4.2.1","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^22.0.0","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","assert":"^2.0.0","browserify":"^17.0.0","c8":"^10.0.0","cors":"^2.8.5","express":"^5.0.0","gts":"^6.0.0","is-docker":"^3.0.0","jsdoc":"^4.0.0","jsdoc-fresh":"^4.0.0","jsdoc-region-tag":"^3.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"^5.0.1","linkinator":"^6.1.2","mocha":"^11.1.0","multiparty":"^4.2.1","mv":"^2.1.1","ncp":"^2.0.0","nock":"^14.0.0-beta.13","null-loader":"^4.0.0","pack-n-play":"^3.0.0","puppeteer":"^24.0.0","sinon":"^20.0.0","stream-browserify":"^3.0.0","tmp":"0.2.3","ts-loader":"^9.5.2","typescript":"^5.8.3","webpack":"^5.35.0","webpack-cli":"^6.0.1"},"dependencies":{"extend":"^3.0.2","https-proxy-agent":"^7.0.1","node-fetch":"^3.3.2"}}');

/***/ })

};
;