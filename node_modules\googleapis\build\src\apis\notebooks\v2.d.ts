import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace notebooks_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Notebooks API
     *
     * Notebooks API is used to manage notebook resources in Google Cloud.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const notebooks = google.notebooks('v2');
     * ```
     */
    export class Notebooks {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * An accelerator configuration for a VM instance Definition of a hardware accelerator. Note that there is no check on `type` and `core_count` combinations. TPUs are not supported. See [GPUs on Compute Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a valid combination.
     */
    export interface Schema$AcceleratorConfig {
        /**
         * Optional. Count of cores of this accelerator.
         */
        coreCount?: string | null;
        /**
         * Optional. Type of this accelerator.
         */
        type?: string | null;
    }
    /**
     * An access configuration attached to an instance's network interface.
     */
    export interface Schema$AccessConfig {
        /**
         * An external IP address associated with this instance. Specify an unused static external IP address available to the project or leave this field undefined to use an IP from a shared ephemeral IP address pool. If you specify a static external IP address, it must live in the same region as the zone of the instance.
         */
        externalIp?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * The definition of a boot disk.
     */
    export interface Schema$BootDisk {
        /**
         * Optional. Input only. Disk encryption method used on the boot and data disks, defaults to GMEK.
         */
        diskEncryption?: string | null;
        /**
         * Optional. The size of the boot disk in GB attached to this instance, up to a maximum of 64000 GB (64 TB). If not specified, this defaults to the recommended value of 150GB.
         */
        diskSizeGb?: string | null;
        /**
         * Optional. Indicates the type of the disk.
         */
        diskType?: string | null;
        /**
         * Optional. Input only. The KMS key used to encrypt the disks, only applicable if disk_encryption is CMEK. Format: `projects/{project_id\}/locations/{location\}/keyRings/{key_ring_id\}/cryptoKeys/{key_id\}` Learn more about using your own encryption keys.
         */
        kmsKey?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Request message for checking authorization for the instance owner.
     */
    export interface Schema$CheckAuthorizationRequest {
        /**
         * Optional. The details of the OAuth authorization response. This may include additional params such as dry_run, version_info, origin, propagate, etc.
         */
        authorizationDetails?: {
            [key: string]: string;
        } | null;
    }
    /**
     * Response message for checking authorization for the instance owner.
     */
    export interface Schema$CheckAuthorizationResponse {
        /**
         * Output only. Timestamp when this Authorization request was created.
         */
        createTime?: string | null;
        /**
         * If the user has not completed OAuth consent, then the oauth_url is returned. Otherwise, this field is not set.
         */
        oauth_uri?: string | null;
        /**
         * Success indicates that the user completed OAuth consent and access tokens can be generated.
         */
        success?: boolean | null;
    }
    /**
     * Response for checking if a notebook instance is upgradeable.
     */
    export interface Schema$CheckInstanceUpgradabilityResponse {
        /**
         * If an instance is upgradeable.
         */
        upgradeable?: boolean | null;
        /**
         * The new image self link this instance will be upgraded to if calling the upgrade endpoint. This field will only be populated if field upgradeable is true.
         */
        upgradeImage?: string | null;
        /**
         * Additional information about upgrade.
         */
        upgradeInfo?: string | null;
        /**
         * The version this instance will be upgraded to if calling the upgrade endpoint. This field will only be populated if field upgradeable is true.
         */
        upgradeVersion?: string | null;
    }
    /**
     * A set of Confidential Instance options.
     */
    export interface Schema$ConfidentialInstanceConfig {
        /**
         * Optional. Defines the type of technology used by the confidential instance.
         */
        confidentialInstanceType?: string | null;
    }
    /**
     * Response for getting WbI configurations in a location
     */
    export interface Schema$Config {
        /**
         * Output only. The list of available images to create a WbI.
         */
        availableImages?: Schema$ImageRelease[];
        /**
         * Output only. The default values for configuration.
         */
        defaultValues?: Schema$DefaultValues;
        /**
         * Output only. Flag to disable the creation of legacy Workbench notebooks (User-managed notebooks and Google-managed notebooks).
         */
        disableWorkbenchLegacyCreation?: boolean | null;
        /**
         * Output only. The supported values for configuration.
         */
        supportedValues?: Schema$SupportedValues;
    }
    /**
     * Definition of a container image for starting a notebook instance with the environment installed in a container.
     */
    export interface Schema$ContainerImage {
        /**
         * Required. The path to the container image repository. For example: `gcr.io/{project_id\}/{image_name\}`
         */
        repository?: string | null;
        /**
         * Optional. The tag of the container image. If not specified, this defaults to the latest tag.
         */
        tag?: string | null;
    }
    /**
     * An instance-attached disk resource.
     */
    export interface Schema$DataDisk {
        /**
         * Optional. Input only. Disk encryption method used on the boot and data disks, defaults to GMEK.
         */
        diskEncryption?: string | null;
        /**
         * Optional. The size of the disk in GB attached to this VM instance, up to a maximum of 64000 GB (64 TB). If not specified, this defaults to 100.
         */
        diskSizeGb?: string | null;
        /**
         * Optional. Input only. Indicates the type of the disk.
         */
        diskType?: string | null;
        /**
         * Optional. Input only. The KMS key used to encrypt the disks, only applicable if disk_encryption is CMEK. Format: `projects/{project_id\}/locations/{location\}/keyRings/{key_ring_id\}/cryptoKeys/{key_id\}` Learn more about using your own encryption keys.
         */
        kmsKey?: string | null;
    }
    /**
     * DefaultValues represents the default configuration values.
     */
    export interface Schema$DefaultValues {
        /**
         * Output only. The default machine type used by the backend if not provided by the user.
         */
        machineType?: string | null;
    }
    /**
     * Request for creating a notebook instance diagnostic file.
     */
    export interface Schema$DiagnoseInstanceRequest {
        /**
         * Required. Defines flags that are used to run the diagnostic tool
         */
        diagnosticConfig?: Schema$DiagnosticConfig;
        /**
         * Optional. Maximum amount of time in minutes before the operation times out.
         */
        timeoutMinutes?: number | null;
    }
    /**
     * Defines flags that are used to run the diagnostic tool
     */
    export interface Schema$DiagnosticConfig {
        /**
         * Optional. Enables flag to copy all `/home/<USER>
         */
        enableCopyHomeFilesFlag?: boolean | null;
        /**
         * Optional. Enables flag to capture packets from the instance for 30 seconds
         */
        enablePacketCaptureFlag?: boolean | null;
        /**
         * Optional. Enables flag to repair service for instance
         */
        enableRepairFlag?: boolean | null;
        /**
         * Required. User Cloud Storage bucket location (REQUIRED). Must be formatted with path prefix (`gs://$GCS_BUCKET`). Permissions: User Managed Notebooks: - storage.buckets.writer: Must be given to the project's service account attached to VM. Google Managed Notebooks: - storage.buckets.writer: Must be given to the project's service account or user credentials attached to VM depending on authentication mode. Cloud Storage bucket Log file will be written to `gs://$GCS_BUCKET/$RELATIVE_PATH/$VM_DATE_$TIME.tar.gz`
         */
        gcsBucket?: string | null;
        /**
         * Optional. Defines the relative storage path in the Cloud Storage bucket where the diagnostic logs will be written: Default path will be the root directory of the Cloud Storage bucket (`gs://$GCS_BUCKET/$DATE_$TIME.tar.gz`) Example of full path where Log file will be written: `gs://$GCS_BUCKET/$RELATIVE_PATH/`
         */
        relativePath?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * The definition of an Event for a managed / semi-managed notebook instance.
     */
    export interface Schema$Event {
        /**
         * Optional. Event details. This field is used to pass event information.
         */
        details?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Event report time.
         */
        reportTime?: string | null;
        /**
         * Optional. Event type.
         */
        type?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * The definition of how to configure a VM instance outside of Resources and Identity.
     */
    export interface Schema$GceSetup {
        /**
         * Optional. The hardware accelerators used on this instance. If you use accelerators, make sure that your configuration has [enough vCPUs and memory to support the `machine_type` you have selected](https://cloud.google.com/compute/docs/gpus/#gpus-list). Currently supports only one accelerator configuration.
         */
        acceleratorConfigs?: Schema$AcceleratorConfig[];
        /**
         * Optional. The boot disk for the VM.
         */
        bootDisk?: Schema$BootDisk;
        /**
         * Optional. Confidential instance configuration.
         */
        confidentialInstanceConfig?: Schema$ConfidentialInstanceConfig;
        /**
         * Optional. Use a container image to start the notebook instance.
         */
        containerImage?: Schema$ContainerImage;
        /**
         * Optional. Data disks attached to the VM instance. Currently supports only one data disk.
         */
        dataDisks?: Schema$DataDisk[];
        /**
         * Optional. If true, no external IP will be assigned to this VM instance.
         */
        disablePublicIp?: boolean | null;
        /**
         * Optional. Flag to enable ip forwarding or not, default false/off. https://cloud.google.com/vpc/docs/using-routes#canipforward
         */
        enableIpForwarding?: boolean | null;
        /**
         * Optional. Configuration for GPU drivers.
         */
        gpuDriverConfig?: Schema$GPUDriverConfig;
        /**
         * Optional. The machine type of the VM instance. https://cloud.google.com/compute/docs/machine-resource
         */
        machineType?: string | null;
        /**
         * Optional. Custom metadata to apply to this instance.
         */
        metadata?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The minimum CPU platform to use for this instance. The list of valid values can be found in https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform#availablezones
         */
        minCpuPlatform?: string | null;
        /**
         * Optional. The network interfaces for the VM. Supports only one interface.
         */
        networkInterfaces?: Schema$NetworkInterface[];
        /**
         * Optional. Specifies the reservations that this instance can consume from.
         */
        reservationAffinity?: Schema$ReservationAffinity;
        /**
         * Optional. The service account that serves as an identity for the VM instance. Currently supports only one service account.
         */
        serviceAccounts?: Schema$ServiceAccount[];
        /**
         * Optional. Shielded VM configuration. [Images using supported Shielded VM features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm).
         */
        shieldedInstanceConfig?: Schema$ShieldedInstanceConfig;
        /**
         * Optional. The Compute Engine network tags to add to runtime (see [Add network tags](https://cloud.google.com/vpc/docs/add-remove-network-tags)).
         */
        tags?: string[] | null;
        /**
         * Optional. Use a Compute Engine VM image to start the notebook instance.
         */
        vmImage?: Schema$VmImage;
    }
    /**
     * Request message for generating an EUC for the instance owner.
     */
    export interface Schema$GenerateAccessTokenRequest {
        /**
         * Required. The VM identity token (a JWT) for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmToken?: string | null;
    }
    /**
     * Response message for generating an EUC for the instance owner.
     */
    export interface Schema$GenerateAccessTokenResponse {
        /**
         * Short-lived access token string which may be used to access Google APIs.
         */
        access_token?: string | null;
        /**
         * The time in seconds when the access token expires. Typically that's 3600.
         */
        expires_in?: number | null;
        /**
         * Space-separated list of scopes contained in the returned token. https://cloud.google.com/docs/authentication/token-types#access-contents
         */
        scope?: string | null;
        /**
         * Type of the returned access token (e.g. "Bearer"). It specifies how the token must be used. Bearer tokens may be used by any entity without proof of identity.
         */
        token_type?: string | null;
    }
    /**
     * A GPU driver configuration
     */
    export interface Schema$GPUDriverConfig {
        /**
         * Optional. Specify a custom Cloud Storage path where the GPU driver is stored. If not specified, we'll automatically choose from official GPU drivers.
         */
        customGpuDriverPath?: string | null;
        /**
         * Optional. Whether the end user authorizes Google Cloud to install GPU driver on this VM instance. If this field is empty or set to false, the GPU driver won't be installed. Only applicable to instances with GPUs.
         */
        enableGpuDriver?: boolean | null;
    }
    /**
     * ConfigImage represents an image release available to create a WbI
     */
    export interface Schema$ImageRelease {
        /**
         * Output only. The name of the image of the form workbench-instances-vYYYYmmdd--
         */
        imageName?: string | null;
        /**
         * Output only. The release of the image of the form m123
         */
        releaseName?: string | null;
    }
    /**
     * The definition of a notebook instance.
     */
    export interface Schema$Instance {
        /**
         * Output only. Instance creation time.
         */
        createTime?: string | null;
        /**
         * Output only. Email address of entity that sent original CreateInstance request.
         */
        creator?: string | null;
        /**
         * Optional. If true, the notebook instance will not register with the proxy.
         */
        disableProxyAccess?: boolean | null;
        /**
         * Optional. If true, deletion protection will be enabled for this Workbench Instance. If false, deletion protection will be disabled for this Workbench Instance.
         */
        enableDeletionProtection?: boolean | null;
        /**
         * Optional. Flag to enable managed end user credentials for the instance.
         */
        enableManagedEuc?: boolean | null;
        /**
         * Optional. Flag that specifies that a notebook can be accessed with third party identity provider.
         */
        enableThirdPartyIdentity?: boolean | null;
        /**
         * Optional. Compute Engine setup for the notebook. Uses notebook-defined fields.
         */
        gceSetup?: Schema$GceSetup;
        /**
         * Output only. Additional information about instance health. Example: healthInfo": { "docker_proxy_agent_status": "1", "docker_status": "1", "jupyterlab_api_status": "-1", "jupyterlab_status": "-1", "updated": "2020-10-18 09:40:03.573409" \}
         */
        healthInfo?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Instance health_state.
         */
        healthState?: string | null;
        /**
         * Output only. Unique ID of the resource.
         */
        id?: string | null;
        /**
         * Optional. The owner of this instance after creation. Format: `<EMAIL>` Currently supports one owner only. If not specified, all of the service account users of your VM instance's service account can use the instance.
         */
        instanceOwners?: string[] | null;
        /**
         * Optional. Labels to apply to this instance. These can be later modified by the UpdateInstance method.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The name of this notebook instance. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string | null;
        /**
         * Output only. The proxy endpoint that is used to access the Jupyter notebook.
         */
        proxyUri?: string | null;
        /**
         * Output only. Reserved for future use for Zone Isolation.
         */
        satisfiesPzi?: boolean | null;
        /**
         * Output only. Reserved for future use for Zone Separation.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. The state of this instance.
         */
        state?: string | null;
        /**
         * Output only. The workforce pools proxy endpoint that is used to access the Jupyter notebook.
         */
        thirdPartyProxyUrl?: string | null;
        /**
         * Output only. Instance update time.
         */
        updateTime?: string | null;
        /**
         * Output only. The upgrade history of this instance.
         */
        upgradeHistory?: Schema$UpgradeHistoryEntry[];
    }
    /**
     * Response for listing notebook instances.
     */
    export interface Schema$ListInstancesResponse {
        /**
         * A list of returned instances.
         */
        instances?: Schema$Instance[];
        /**
         * Page token that can be used to continue listing from the last result in the next list call.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached. For example, ['us-west1-a', 'us-central1-b']. A ListInstancesResponse will only contain either instances or unreachables,
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * The definition of a network interface resource attached to a VM.
     */
    export interface Schema$NetworkInterface {
        /**
         * Optional. An array of configurations for this interface. Currently, only one access config, ONE_TO_ONE_NAT, is supported. If no accessConfigs specified, the instance will have an external internet access through an ephemeral external IP address.
         */
        accessConfigs?: Schema$AccessConfig[];
        /**
         * Optional. The name of the VPC that this VM instance is in. Format: `projects/{project_id\}/global/networks/{network_id\}`
         */
        network?: string | null;
        /**
         * Optional. The type of vNIC to be used on this interface. This may be gVNIC or VirtioNet.
         */
        nicType?: string | null;
        /**
         * Optional. The name of the subnet that this VM instance is in. Format: `projects/{project_id\}/regions/{region\}/subnetworks/{subnetwork_id\}`
         */
        subnet?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * The time the operation was created.
         */
        createTime?: string | null;
        /**
         * API endpoint name of this operation.
         */
        endpoint?: string | null;
        /**
         * The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Request for notebook instances to report information to Notebooks API.
     */
    export interface Schema$ReportInstanceInfoSystemRequest {
        /**
         * Required. The Event to be reported.
         */
        event?: Schema$Event;
        /**
         * Required. The VM hardware token for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmId?: string | null;
    }
    /**
     * A reservation that an instance can consume from.
     */
    export interface Schema$ReservationAffinity {
        /**
         * Required. Specifies the type of reservation from which this instance can consume resources: RESERVATION_ANY (default), RESERVATION_SPECIFIC, or RESERVATION_NONE. See Consuming reserved instances for examples.
         */
        consumeReservationType?: string | null;
        /**
         * Optional. Corresponds to the label key of a reservation resource. To target a RESERVATION_SPECIFIC by name, use compute.googleapis.com/reservation-name as the key and specify the name of your reservation as its value.
         */
        key?: string | null;
        /**
         * Optional. Corresponds to the label values of a reservation resource. This can be either a name to a reservation in the same project or "projects/different-project/reservations/some-reservation-name" to target a shared reservation in the same zone but in a different project.
         */
        values?: string[] | null;
    }
    /**
     * Request for resetting a notebook instance
     */
    export interface Schema$ResetInstanceRequest {
    }
    /**
     * Request for resizing the notebook instance disks
     */
    export interface Schema$ResizeDiskRequest {
        /**
         * Required. The boot disk to be resized. Only disk_size_gb will be used.
         */
        bootDisk?: Schema$BootDisk;
        /**
         * Required. The data disk to be resized. Only disk_size_gb will be used.
         */
        dataDisk?: Schema$DataDisk;
    }
    /**
     * Request for restoring the notebook instance from a BackupSource.
     */
    export interface Schema$RestoreInstanceRequest {
        /**
         * Snapshot to be used for restore.
         */
        snapshot?: Schema$Snapshot;
    }
    /**
     * Request for rollbacking a notebook instance
     */
    export interface Schema$RollbackInstanceRequest {
        /**
         * Required. Output only. Revision Id
         */
        revisionId?: string | null;
        /**
         * Required. The snapshot for rollback. Example: "projects/test-project/global/snapshots/krwlzipynril".
         */
        targetSnapshot?: string | null;
    }
    /**
     * A service account that acts as an identity.
     */
    export interface Schema$ServiceAccount {
        /**
         * Optional. Email address of the service account.
         */
        email?: string | null;
        /**
         * Output only. The list of scopes to be made available for this service account. Set by the CLH to https://www.googleapis.com/auth/cloud-platform
         */
        scopes?: string[] | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
    }
    /**
     * A set of Shielded Instance options. See [Images using supported Shielded VM features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm). Not all combinations are valid.
     */
    export interface Schema$ShieldedInstanceConfig {
        /**
         * Optional. Defines whether the VM instance has integrity monitoring enabled. Enables monitoring and attestation of the boot integrity of the VM instance. The attestation is performed against the integrity policy baseline. This baseline is initially derived from the implicitly trusted boot image when the VM instance is created.
         */
        enableIntegrityMonitoring?: boolean | null;
        /**
         * Optional. Defines whether the VM instance has Secure Boot enabled. Secure Boot helps ensure that the system only runs authentic software by verifying the digital signature of all boot components, and halting the boot process if signature verification fails. Disabled by default.
         */
        enableSecureBoot?: boolean | null;
        /**
         * Optional. Defines whether the VM instance has the vTPM enabled.
         */
        enableVtpm?: boolean | null;
    }
    /**
     * Snapshot represents the snapshot of the data disk used to restore the Workbench Instance from. Refers to: compute/v1/projects/{project_id\}/global/snapshots/{snapshot_id\}
     */
    export interface Schema$Snapshot {
        /**
         * Required. The project ID of the snapshot.
         */
        projectId?: string | null;
        /**
         * Required. The ID of the snapshot.
         */
        snapshotId?: string | null;
    }
    /**
     * Request for starting a notebook instance
     */
    export interface Schema$StartInstanceRequest {
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request for stopping a notebook instance
     */
    export interface Schema$StopInstanceRequest {
    }
    /**
     * SupportedValues represents the values supported by the configuration.
     */
    export interface Schema$SupportedValues {
        /**
         * Output only. The accelerator types supported by WbI.
         */
        acceleratorTypes?: string[] | null;
        /**
         * Output only. The machine types supported by WbI.
         */
        machineTypes?: string[] | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * The entry of VM image upgrade history.
     */
    export interface Schema$UpgradeHistoryEntry {
        /**
         * Optional. Action. Rolloback or Upgrade.
         */
        action?: string | null;
        /**
         * Optional. The container image before this instance upgrade.
         */
        containerImage?: string | null;
        /**
         * Immutable. The time that this instance upgrade history entry is created.
         */
        createTime?: string | null;
        /**
         * Optional. The framework of this notebook instance.
         */
        framework?: string | null;
        /**
         * Optional. The snapshot of the boot disk of this notebook instance before upgrade.
         */
        snapshot?: string | null;
        /**
         * Output only. The state of this instance upgrade history entry.
         */
        state?: string | null;
        /**
         * Optional. Target VM Version, like m63.
         */
        targetVersion?: string | null;
        /**
         * Optional. The version of the notebook instance before this upgrade.
         */
        version?: string | null;
        /**
         * Optional. The VM image before this instance upgrade.
         */
        vmImage?: string | null;
    }
    /**
     * Request for upgrading a notebook instance
     */
    export interface Schema$UpgradeInstanceRequest {
    }
    /**
     * Request for upgrading a notebook instance from within the VM
     */
    export interface Schema$UpgradeInstanceSystemRequest {
        /**
         * Required. The VM hardware token for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmId?: string | null;
    }
    /**
     * Definition of a custom Compute Engine virtual machine image for starting a notebook instance with the environment installed directly on the VM.
     */
    export interface Schema$VmImage {
        /**
         * Optional. Use this VM image family to find the image; the newest image in this family will be used.
         */
        family?: string | null;
        /**
         * Optional. Use VM image name to find the image.
         */
        name?: string | null;
        /**
         * Required. The name of the Google Cloud project that this VM image belongs to. Format: `{project_id\}`
         */
        project?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        instances: Resource$Projects$Locations$Instances;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.get({
         *     // Resource name for the location.
         *     name: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "displayName": "my_displayName",
         *   //   "labels": {},
         *   //   "locationId": "my_locationId",
         *   //   "metadata": {},
         *   //   "name": "my_name"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.list({
         *     // Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         *     extraLocationTypes: 'placeholder-value',
         *     // A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         *     filter: 'placeholder-value',
         *     // The resource that owns the locations collection, if applicable.
         *     name: 'projects/my-project',
         *     // The maximum number of results to return. If not set, the service selects a default.
         *     pageSize: 'placeholder-value',
         *     // A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "locations": [],
         *   //   "nextPageToken": "my_nextPageToken"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Instances {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Initiated by Cloud Console for Oauth consent flow for Workbench Instances. Do not use this method directly. Design doc: go/wbi-euc:auth-dd
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.checkAuthorization({
         *     // Required. The name of the Notebook Instance resource. Format: `projects/{project\}/locations/{location\}/instances/{instance\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "authorizationDetails": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "oauth_uri": "my_oauth_uri",
         *   //   "success": false
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        checkAuthorization(params: Params$Resource$Projects$Locations$Instances$Checkauthorization, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        checkAuthorization(params?: Params$Resource$Projects$Locations$Instances$Checkauthorization, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$CheckAuthorizationResponse>>;
        checkAuthorization(params: Params$Resource$Projects$Locations$Instances$Checkauthorization, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        checkAuthorization(params: Params$Resource$Projects$Locations$Instances$Checkauthorization, options: MethodOptions | BodyResponseCallback<Schema$CheckAuthorizationResponse>, callback: BodyResponseCallback<Schema$CheckAuthorizationResponse>): void;
        checkAuthorization(params: Params$Resource$Projects$Locations$Instances$Checkauthorization, callback: BodyResponseCallback<Schema$CheckAuthorizationResponse>): void;
        checkAuthorization(callback: BodyResponseCallback<Schema$CheckAuthorizationResponse>): void;
        /**
         * Checks whether a notebook instance is upgradable.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.checkUpgradability({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     notebookInstance:
         *       'projects/my-project/locations/my-location/instances/my-instance',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "upgradeImage": "my_upgradeImage",
         *   //   "upgradeInfo": "my_upgradeInfo",
         *   //   "upgradeVersion": "my_upgradeVersion",
         *   //   "upgradeable": false
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        checkUpgradability(params: Params$Resource$Projects$Locations$Instances$Checkupgradability, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        checkUpgradability(params?: Params$Resource$Projects$Locations$Instances$Checkupgradability, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$CheckInstanceUpgradabilityResponse>>;
        checkUpgradability(params: Params$Resource$Projects$Locations$Instances$Checkupgradability, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        checkUpgradability(params: Params$Resource$Projects$Locations$Instances$Checkupgradability, options: MethodOptions | BodyResponseCallback<Schema$CheckInstanceUpgradabilityResponse>, callback: BodyResponseCallback<Schema$CheckInstanceUpgradabilityResponse>): void;
        checkUpgradability(params: Params$Resource$Projects$Locations$Instances$Checkupgradability, callback: BodyResponseCallback<Schema$CheckInstanceUpgradabilityResponse>): void;
        checkUpgradability(callback: BodyResponseCallback<Schema$CheckInstanceUpgradabilityResponse>): void;
        /**
         * Creates a new Instance in a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.create({
         *     // Required. User-defined unique ID of this instance.
         *     instanceId: 'placeholder-value',
         *     // Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         *     parent: 'projects/my-project/locations/my-location',
         *     // Optional. Idempotent request UUID.
         *     requestId: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "creator": "my_creator",
         *       //   "disableProxyAccess": false,
         *       //   "enableDeletionProtection": false,
         *       //   "enableManagedEuc": false,
         *       //   "enableThirdPartyIdentity": false,
         *       //   "gceSetup": {},
         *       //   "healthInfo": {},
         *       //   "healthState": "my_healthState",
         *       //   "id": "my_id",
         *       //   "instanceOwners": [],
         *       //   "labels": {},
         *       //   "name": "my_name",
         *       //   "proxyUri": "my_proxyUri",
         *       //   "satisfiesPzi": false,
         *       //   "satisfiesPzs": false,
         *       //   "state": "my_state",
         *       //   "thirdPartyProxyUrl": "my_thirdPartyProxyUrl",
         *       //   "updateTime": "my_updateTime",
         *       //   "upgradeHistory": []
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Instances$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single Instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.delete({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *     // Optional. Idempotent request UUID.
         *     requestId: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Instances$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a Diagnostic File and runs Diagnostic Tool given an Instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.diagnose({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "diagnosticConfig": {},
         *       //   "timeoutMinutes": 0
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        diagnose(params?: Params$Resource$Projects$Locations$Instances$Diagnose, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, callback: BodyResponseCallback<Schema$Operation>): void;
        diagnose(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Called by VM to return an EUC for the instance owner. Do not use this method directly. Design doc: go/wbi-euc:dd
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.generateAccessToken({
         *     // Required. Format: `projects/{project\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "vmToken": "my_vmToken"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "access_token": "my_access_token",
         *   //   "expires_in": 0,
         *   //   "scope": "my_scope",
         *   //   "token_type": "my_token_type"
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generateAccessToken(params: Params$Resource$Projects$Locations$Instances$Generateaccesstoken, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        generateAccessToken(params?: Params$Resource$Projects$Locations$Instances$Generateaccesstoken, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GenerateAccessTokenResponse>>;
        generateAccessToken(params: Params$Resource$Projects$Locations$Instances$Generateaccesstoken, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generateAccessToken(params: Params$Resource$Projects$Locations$Instances$Generateaccesstoken, options: MethodOptions | BodyResponseCallback<Schema$GenerateAccessTokenResponse>, callback: BodyResponseCallback<Schema$GenerateAccessTokenResponse>): void;
        generateAccessToken(params: Params$Resource$Projects$Locations$Instances$Generateaccesstoken, callback: BodyResponseCallback<Schema$GenerateAccessTokenResponse>): void;
        generateAccessToken(callback: BodyResponseCallback<Schema$GenerateAccessTokenResponse>): void;
        /**
         * Gets details of a single Instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.get({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "createTime": "my_createTime",
         *   //   "creator": "my_creator",
         *   //   "disableProxyAccess": false,
         *   //   "enableDeletionProtection": false,
         *   //   "enableManagedEuc": false,
         *   //   "enableThirdPartyIdentity": false,
         *   //   "gceSetup": {},
         *   //   "healthInfo": {},
         *   //   "healthState": "my_healthState",
         *   //   "id": "my_id",
         *   //   "instanceOwners": [],
         *   //   "labels": {},
         *   //   "name": "my_name",
         *   //   "proxyUri": "my_proxyUri",
         *   //   "satisfiesPzi": false,
         *   //   "satisfiesPzs": false,
         *   //   "state": "my_state",
         *   //   "thirdPartyProxyUrl": "my_thirdPartyProxyUrl",
         *   //   "updateTime": "my_updateTime",
         *   //   "upgradeHistory": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Instances$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Instance>>;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: MethodOptions | BodyResponseCallback<Schema$Instance>, callback: BodyResponseCallback<Schema$Instance>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, callback: BodyResponseCallback<Schema$Instance>): void;
        get(callback: BodyResponseCallback<Schema$Instance>): void;
        /**
         * Returns various configuration parameters.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.getConfig({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}`
         *     name: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "availableImages": [],
         *   //   "defaultValues": {},
         *   //   "disableWorkbenchLegacyCreation": false,
         *   //   "supportedValues": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getConfig(params: Params$Resource$Projects$Locations$Instances$Getconfig, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getConfig(params?: Params$Resource$Projects$Locations$Instances$Getconfig, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Config>>;
        getConfig(params: Params$Resource$Projects$Locations$Instances$Getconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getConfig(params: Params$Resource$Projects$Locations$Instances$Getconfig, options: MethodOptions | BodyResponseCallback<Schema$Config>, callback: BodyResponseCallback<Schema$Config>): void;
        getConfig(params: Params$Resource$Projects$Locations$Instances$Getconfig, callback: BodyResponseCallback<Schema$Config>): void;
        getConfig(callback: BodyResponseCallback<Schema$Config>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.getIamPolicy({
         *     // Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         *     'options.requestedPolicyVersion': 'placeholder-value',
         *     // REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'projects/my-project/locations/my-location/instances/my-instance',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Instances$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists instances in a given project and location.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.list({
         *     // Optional. List filter.
         *     filter: 'placeholder-value',
         *     // Optional. Sort results. Supported values are "name", "name desc" or "" (unsorted).
         *     orderBy: 'placeholder-value',
         *     // Optional. Maximum return size of the list call.
         *     pageSize: 'placeholder-value',
         *     // Optional. A previous returned page token that can be used to continue listing from the last result.
         *     pageToken: 'placeholder-value',
         *     // Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         *     parent: 'projects/my-project/locations/my-location',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "instances": [],
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "unreachable": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Instances$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListInstancesResponse>>;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: MethodOptions | BodyResponseCallback<Schema$ListInstancesResponse>, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        /**
         * UpdateInstance updates an Instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.patch({
         *     // Output only. The name of this notebook instance. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *     // Optional. Idempotent request UUID.
         *     requestId: 'placeholder-value',
         *     // Required. Mask used to update an instance. Updatable fields: * `labels` * `gce_setup.min_cpu_platform` * `gce_setup.metadata` * `gce_setup.machine_type` * `gce_setup.accelerator_configs` * `gce_setup.accelerator_configs.type` * `gce_setup.accelerator_configs.core_count` * `gce_setup.gpu_driver_config` * `gce_setup.gpu_driver_config.enable_gpu_driver` * `gce_setup.gpu_driver_config.custom_gpu_driver_path` * `gce_setup.shielded_instance_config` * `gce_setup.shielded_instance_config.enable_secure_boot` * `gce_setup.shielded_instance_config.enable_vtpm` * `gce_setup.shielded_instance_config.enable_integrity_monitoring` * `gce_setup.reservation_affinity` * `gce_setup.reservation_affinity.consume_reservation_type` * `gce_setup.reservation_affinity.key` * `gce_setup.reservation_affinity.values` * `gce_setup.tags` * `gce_setup.container_image` * `gce_setup.container_image.repository` * `gce_setup.container_image.tag` * `gce_setup.disable_public_ip` * `disable_proxy_access`
         *     updateMask: 'placeholder-value',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "createTime": "my_createTime",
         *       //   "creator": "my_creator",
         *       //   "disableProxyAccess": false,
         *       //   "enableDeletionProtection": false,
         *       //   "enableManagedEuc": false,
         *       //   "enableThirdPartyIdentity": false,
         *       //   "gceSetup": {},
         *       //   "healthInfo": {},
         *       //   "healthState": "my_healthState",
         *       //   "id": "my_id",
         *       //   "instanceOwners": [],
         *       //   "labels": {},
         *       //   "name": "my_name",
         *       //   "proxyUri": "my_proxyUri",
         *       //   "satisfiesPzi": false,
         *       //   "satisfiesPzs": false,
         *       //   "state": "my_state",
         *       //   "thirdPartyProxyUrl": "my_thirdPartyProxyUrl",
         *       //   "updateTime": "my_updateTime",
         *       //   "upgradeHistory": []
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Instances$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Allows notebook instances to report their latest instance information to the Notebooks API server. The server will merge the reported information to the instance metadata store. Do not use this method directly.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.reportInfoSystem({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "event": {},
         *       //   "vmId": "my_vmId"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reportInfoSystem(params: Params$Resource$Projects$Locations$Instances$Reportinfosystem, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        reportInfoSystem(params?: Params$Resource$Projects$Locations$Instances$Reportinfosystem, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        reportInfoSystem(params: Params$Resource$Projects$Locations$Instances$Reportinfosystem, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reportInfoSystem(params: Params$Resource$Projects$Locations$Instances$Reportinfosystem, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reportInfoSystem(params: Params$Resource$Projects$Locations$Instances$Reportinfosystem, callback: BodyResponseCallback<Schema$Operation>): void;
        reportInfoSystem(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Resets a notebook instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.reset({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        reset(params?: Params$Resource$Projects$Locations$Instances$Reset, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, callback: BodyResponseCallback<Schema$Operation>): void;
        reset(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Resize a notebook instance disk to a higher capacity.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.resizeDisk({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     notebookInstance:
         *       'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "bootDisk": {},
         *       //   "dataDisk": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resizeDisk(params: Params$Resource$Projects$Locations$Instances$Resizedisk, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        resizeDisk(params?: Params$Resource$Projects$Locations$Instances$Resizedisk, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        resizeDisk(params: Params$Resource$Projects$Locations$Instances$Resizedisk, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resizeDisk(params: Params$Resource$Projects$Locations$Instances$Resizedisk, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        resizeDisk(params: Params$Resource$Projects$Locations$Instances$Resizedisk, callback: BodyResponseCallback<Schema$Operation>): void;
        resizeDisk(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * RestoreInstance restores an Instance from a BackupSource.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.restore({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "snapshot": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        restore(params?: Params$Resource$Projects$Locations$Instances$Restore, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Rollbacks a notebook instance to the previous version.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.rollback({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "revisionId": "my_revisionId",
         *       //   "targetSnapshot": "my_targetSnapshot"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        rollback(params?: Params$Resource$Projects$Locations$Instances$Rollback, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, callback: BodyResponseCallback<Schema$Operation>): void;
        rollback(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.setIamPolicy({
         *     // REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "policy": {}
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "bindings": [],
         *   //   "etag": "my_etag",
         *   //   "version": 0
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Instances$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Starts a notebook instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.start({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        start(params: Params$Resource$Projects$Locations$Instances$Start, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        start(params?: Params$Resource$Projects$Locations$Instances$Start, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        start(params: Params$Resource$Projects$Locations$Instances$Start, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        start(params: Params$Resource$Projects$Locations$Instances$Start, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        start(params: Params$Resource$Projects$Locations$Instances$Start, callback: BodyResponseCallback<Schema$Operation>): void;
        start(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Stops a notebook instance.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.stop({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        stop(params?: Params$Resource$Projects$Locations$Instances$Stop, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.testIamPermissions({
         *     // REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         *     resource: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "permissions": []
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "permissions": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Instances$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Upgrades a notebook instance to the latest version.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.upgrade({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        upgrade(params?: Params$Resource$Projects$Locations$Instances$Upgrade, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, callback: BodyResponseCallback<Schema$Operation>): void;
        upgrade(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Allows notebook instances to upgrade themselves. Do not use this method directly.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.instances.upgradeSystem({
         *     // Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         *     name: 'projects/my-project/locations/my-location/instances/my-instance',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {
         *       //   "vmId": "my_vmId"
         *       // }
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        upgradeSystem(params: Params$Resource$Projects$Locations$Instances$Upgradesystem, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        upgradeSystem(params?: Params$Resource$Projects$Locations$Instances$Upgradesystem, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        upgradeSystem(params: Params$Resource$Projects$Locations$Instances$Upgradesystem, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        upgradeSystem(params: Params$Resource$Projects$Locations$Instances$Upgradesystem, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        upgradeSystem(params: Params$Resource$Projects$Locations$Instances$Upgradesystem, callback: BodyResponseCallback<Schema$Operation>): void;
        upgradeSystem(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Instances$Checkauthorization extends StandardParameters {
        /**
         * Required. The name of the Notebook Instance resource. Format: `projects/{project\}/locations/{location\}/instances/{instance\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CheckAuthorizationRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Checkupgradability extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        notebookInstance?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Create extends StandardParameters {
        /**
         * Required. User-defined unique ID of this instance.
         */
        instanceId?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Optional. Idempotent request UUID.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export interface Params$Resource$Projects$Locations$Instances$Delete extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Optional. Idempotent request UUID.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Diagnose extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DiagnoseInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Generateaccesstoken extends StandardParameters {
        /**
         * Required. Format: `projects/{project\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GenerateAccessTokenRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Get extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Getconfig extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$List extends StandardParameters {
        /**
         * Optional. List filter.
         */
        filter?: string;
        /**
         * Optional. Sort results. Supported values are "name", "name desc" or "" (unsorted).
         */
        orderBy?: string;
        /**
         * Optional. Maximum return size of the list call.
         */
        pageSize?: number;
        /**
         * Optional. A previous returned page token that can be used to continue listing from the last result.
         */
        pageToken?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Patch extends StandardParameters {
        /**
         * Output only. The name of this notebook instance. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Optional. Idempotent request UUID.
         */
        requestId?: string;
        /**
         * Required. Mask used to update an instance. Updatable fields: * `labels` * `gce_setup.min_cpu_platform` * `gce_setup.metadata` * `gce_setup.machine_type` * `gce_setup.accelerator_configs` * `gce_setup.accelerator_configs.type` * `gce_setup.accelerator_configs.core_count` * `gce_setup.gpu_driver_config` * `gce_setup.gpu_driver_config.enable_gpu_driver` * `gce_setup.gpu_driver_config.custom_gpu_driver_path` * `gce_setup.shielded_instance_config` * `gce_setup.shielded_instance_config.enable_secure_boot` * `gce_setup.shielded_instance_config.enable_vtpm` * `gce_setup.shielded_instance_config.enable_integrity_monitoring` * `gce_setup.reservation_affinity` * `gce_setup.reservation_affinity.consume_reservation_type` * `gce_setup.reservation_affinity.key` * `gce_setup.reservation_affinity.values` * `gce_setup.tags` * `gce_setup.container_image` * `gce_setup.container_image.repository` * `gce_setup.container_image.tag` * `gce_setup.disable_public_ip` * `disable_proxy_access`
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export interface Params$Resource$Projects$Locations$Instances$Reportinfosystem extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ReportInstanceInfoSystemRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Reset extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ResetInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Resizedisk extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        notebookInstance?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ResizeDiskRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Restore extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RestoreInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Rollback extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RollbackInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Start extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StartInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Stop extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StopInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Upgrade extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpgradeInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Upgradesystem extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpgradeInstanceSystemRequest;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.operations.cancel({
         *     // The name of the operation resource to be cancelled.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *
         *     // Request body metadata
         *     requestBody: {
         *       // request body parameters
         *       // {}
         *     },
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.operations.delete({
         *     // The name of the operation resource to be deleted.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {}
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.operations.get({
         *     // The name of the operation resource.
         *     name: 'projects/my-project/locations/my-location/operations/my-operation',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "done": false,
         *   //   "error": {},
         *   //   "metadata": {},
         *   //   "name": "my_name",
         *   //   "response": {}
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         * @example
         * ```js
         * // Before running the sample:
         * // - Enable the API at:
         * //   https://console.developers.google.com/apis/api/notebooks.googleapis.com
         * // - Login into gcloud by running:
         * //   ```sh
         * //   $ gcloud auth application-default login
         * //   ```
         * // - Install the npm module by running:
         * //   ```sh
         * //   $ npm install googleapis
         * //   ```
         *
         * const {google} = require('googleapis');
         * const notebooks = google.notebooks('v2');
         *
         * async function main() {
         *   const auth = new google.auth.GoogleAuth({
         *     // Scopes can be specified either as an array or as a single, space-delimited string.
         *     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
         *   });
         *
         *   // Acquire an auth client, and bind it to all future calls
         *   const authClient = await auth.getClient();
         *   google.options({auth: authClient});
         *
         *   // Do the magic
         *   const res = await notebooks.projects.locations.operations.list({
         *     // The standard list filter.
         *     filter: 'placeholder-value',
         *     // The name of the operation's parent resource.
         *     name: 'projects/my-project/locations/my-location',
         *     // The standard list page size.
         *     pageSize: 'placeholder-value',
         *     // The standard list page token.
         *     pageToken: 'placeholder-value',
         *   });
         *   console.log(res.data);
         *
         *   // Example response
         *   // {
         *   //   "nextPageToken": "my_nextPageToken",
         *   //   "operations": []
         *   // }
         * }
         *
         * main().catch(e => {
         *   console.error(e);
         *   throw e;
         * });
         *
         * ```
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export {};
}
