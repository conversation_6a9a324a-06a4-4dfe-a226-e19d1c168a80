# Deployment Guide

This guide covers deploying the Document Signing System to production using Vercel and GitHub.

## Prerequisites

- Completed local development setup
- GitHub account
- Vercel account
- All Google services configured
- Supabase project set up

## Pre-Deployment Checklist

### 1. Code Preparation
- [ ] All features tested locally
- [ ] Environment variables configured
- [ ] Database schema deployed to production Supabase
- [ ] Google services configured for production domain
- [ ] Admin users created in production database

### 2. Environment Configuration
- [ ] Production Supabase project created
- [ ] Google OAuth configured with production URLs
- [ ] Google Drive folder created and shared
- [ ] Google Sheets created and configured
- [ ] Google Apps Script deployed

## Step-by-Step Deployment

### 1. Prepare GitHub Repository

```bash
# Initialize git repository (if not already done)
git init

# Add all files
git add .

# Commit changes
git commit -m "Initial commit: Document Signing System"

# Add remote repository
git remote add origin https://github.com/yourusername/document-signing-system.git

# Push to GitHub
git push -u origin main
```

### 2. Configure Production Environment

#### Update Google OAuth Settings
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add production redirect URI:
   ```
   https://your-domain.vercel.app/api/auth/callback/google
   ```

#### Update Google Apps Script
1. Open your Google Sheet
2. Go to Extensions > Apps Script
3. Update the `CONFIG` object with production values:
   ```javascript
   const CONFIG = {
     SHEET_NAME: 'Document Tracking',
     WEBHOOK_URL: 'https://your-domain.vercel.app/api/webhook/sheets',
     DRIVE_FOLDER_ID: 'your-production-drive-folder-id'
   }
   ```

### 3. Deploy to Vercel

#### Option A: Vercel Dashboard
1. Go to [vercel.com](https://vercel.com)
2. Click "New Project"
3. Import your GitHub repository
4. Configure project settings:
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`
   - Install Command: `npm install`

#### Option B: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

### 4. Configure Environment Variables

In Vercel Dashboard, go to Project Settings > Environment Variables and add:

```env
# Database
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_supabase_service_role_key

# NextAuth
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your_production_nextauth_secret

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Google Services
GOOGLE_SERVICE_ACCOUNT_EMAIL=your_service_account_email
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
GOOGLE_DRIVE_FOLDER_ID=your_production_drive_folder_id
GOOGLE_SHEETS_ID=your_production_sheets_id
```

### 5. Database Migration

#### Production Database Setup
1. Create production Supabase project
2. Run the database schema:
   ```sql
   -- Copy content from database-schema.sql
   ```
3. Update admin user email in the schema
4. Enable Row Level Security policies

#### Data Migration (if needed)
```bash
# Export from development
supabase db dump --project-id dev-project-id > backup.sql

# Import to production
supabase db reset --project-id prod-project-id
psql -h your-prod-db-host -U postgres -d postgres < backup.sql
```

### 6. Domain Configuration

#### Custom Domain (Optional)
1. In Vercel Dashboard, go to Project Settings > Domains
2. Add your custom domain
3. Configure DNS records as instructed
4. Update all OAuth redirect URIs to use custom domain

### 7. Post-Deployment Verification

#### Test Core Functionality
- [ ] Authentication works with Google OAuth
- [ ] User can upload documents
- [ ] Files are stored in Google Drive
- [ ] Google Sheets is updated
- [ ] Admin can sign documents
- [ ] Email notifications work (if configured)

#### Performance Checks
- [ ] Page load times are acceptable
- [ ] File uploads work smoothly
- [ ] API responses are fast
- [ ] Mobile experience is good

### 8. Monitoring and Maintenance

#### Set Up Monitoring
1. **Vercel Analytics**: Enable in project settings
2. **Error Tracking**: Consider Sentry integration
3. **Uptime Monitoring**: Use services like UptimeRobot

#### Regular Maintenance
- Monitor Google API quotas
- Check Supabase usage limits
- Update dependencies regularly
- Monitor error logs

## Environment-Specific Configurations

### Development
```env
NEXTAUTH_URL=http://localhost:3000
```

### Staging
```env
NEXTAUTH_URL=https://staging-your-app.vercel.app
```

### Production
```env
NEXTAUTH_URL=https://your-domain.com
```

## Troubleshooting Deployment Issues

### Common Issues

#### Build Failures
```bash
# Check build logs in Vercel dashboard
# Common fixes:
npm run type-check  # Fix TypeScript errors
npm run lint        # Fix linting errors
```

#### Authentication Issues
- Verify OAuth redirect URIs
- Check NEXTAUTH_URL matches deployment URL
- Ensure NEXTAUTH_SECRET is set

#### API Errors
- Check environment variables are set correctly
- Verify Google service account permissions
- Check Supabase connection strings

#### File Upload Issues
- Verify Google Drive API quotas
- Check service account permissions
- Ensure folder sharing is correct

### Debug Commands

```bash
# Check deployment logs
vercel logs your-deployment-url

# Test API endpoints
curl https://your-domain.vercel.app/api/documents

# Check environment variables
vercel env ls
```

## Security Considerations

### Production Security
- [ ] Use strong NEXTAUTH_SECRET
- [ ] Enable HTTPS only
- [ ] Configure proper CORS headers
- [ ] Set up rate limiting (if needed)
- [ ] Regular security updates

### Google Services Security
- [ ] Limit service account permissions
- [ ] Use separate production credentials
- [ ] Monitor API usage and quotas
- [ ] Regular access reviews

## Backup Strategy

### Database Backups
```bash
# Automated backups via Supabase
# Manual backup
supabase db dump --project-id your-project-id > backup-$(date +%Y%m%d).sql
```

### File Backups
- Google Drive provides automatic versioning
- Consider additional backup strategy for critical documents

## Scaling Considerations

### Performance Optimization
- Enable Vercel Edge Functions for API routes
- Implement caching strategies
- Optimize image and file handling
- Consider CDN for static assets

### Database Scaling
- Monitor Supabase usage
- Implement connection pooling if needed
- Consider read replicas for heavy read workloads

## Support and Maintenance

### Regular Tasks
- [ ] Monitor error logs weekly
- [ ] Check API quotas monthly
- [ ] Update dependencies quarterly
- [ ] Review security settings quarterly

### Emergency Procedures
1. **Service Outage**: Check Vercel status, Google APIs status
2. **Data Issues**: Restore from backups
3. **Security Breach**: Rotate all credentials immediately

## Cost Optimization

### Vercel Costs
- Monitor function execution time
- Optimize build times
- Use appropriate plan for usage

### Google API Costs
- Monitor API usage
- Implement caching where possible
- Set up billing alerts

### Supabase Costs
- Monitor database size and requests
- Optimize queries
- Clean up old data regularly
