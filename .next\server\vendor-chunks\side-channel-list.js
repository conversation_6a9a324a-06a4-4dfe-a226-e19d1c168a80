"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-list";
exports.ids = ["vendor-chunks/side-channel-list"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-list/index.js":
/*!*************************************************!*\
  !*** ./node_modules/side-channel-list/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\n\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\n\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/\n/** @type {import('./list.d.ts').listGetNode} */\n// eslint-disable-next-line consistent-return\nvar listGetNode = function (list, key, isDelete) {\n\t/** @type {typeof list | NonNullable<(typeof list)['next']>} */\n\tvar prev = list;\n\t/** @type {(typeof list)['next']} */\n\tvar curr;\n\t// eslint-disable-next-line eqeqeq\n\tfor (; (curr = prev.next) != null; prev = curr) {\n\t\tif (curr.key === key) {\n\t\t\tprev.next = curr.next;\n\t\t\tif (!isDelete) {\n\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\tcurr.next = /** @type {NonNullable<typeof list.next>} */ (list.next);\n\t\t\t\tlist.next = curr; // eslint-disable-line no-param-reassign\n\t\t\t}\n\t\t\treturn curr;\n\t\t}\n\t}\n};\n\n/** @type {import('./list.d.ts').listGet} */\nvar listGet = function (objects, key) {\n\tif (!objects) {\n\t\treturn void undefined;\n\t}\n\tvar node = listGetNode(objects, key);\n\treturn node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */\nvar listSet = function (objects, key, value) {\n\tvar node = listGetNode(objects, key);\n\tif (node) {\n\t\tnode.value = value;\n\t} else {\n\t\t// Prepend the new node to the beginning of the list\n\t\tobjects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */ ({ // eslint-disable-line no-param-reassign, no-extra-parens\n\t\t\tkey: key,\n\t\t\tnext: objects.next,\n\t\t\tvalue: value\n\t\t});\n\t}\n};\n/** @type {import('./list.d.ts').listHas} */\nvar listHas = function (objects, key) {\n\tif (!objects) {\n\t\treturn false;\n\t}\n\treturn !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */\n// eslint-disable-next-line consistent-return\nvar listDelete = function (objects, key) {\n\tif (objects) {\n\t\treturn listGetNode(objects, key, true);\n\t}\n};\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannelList() {\n\t/** @typedef {ReturnType<typeof getSideChannelList>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {import('./list.d.ts').RootNode<V, K> | undefined} */ var $o;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tvar root = $o && $o.next;\n\t\t\tvar deletedNode = listDelete($o, key);\n\t\t\tif (deletedNode && root && root === deletedNode) {\n\t\t\t\t$o = void undefined;\n\t\t\t}\n\t\t\treturn !!deletedNode;\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn listGet($o, key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn listHas($o, key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$o) {\n\t\t\t\t// Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n\t\t\t\t$o = {\n\t\t\t\t\tnext: void undefined\n\t\t\t\t};\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\tlistSet(/** @type {NonNullable<typeof $o>} */ ($o), key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-list/index.js\n");

/***/ })

};
;