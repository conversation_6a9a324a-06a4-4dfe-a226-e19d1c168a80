"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/agent-base";
exports.ids = ["vendor-chunks/agent-base"];
exports.modules = {

/***/ "(rsc)/./node_modules/agent-base/dist/helpers.js":
/*!*************************************************!*\
  !*** ./node_modules/agent-base/dist/helpers.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.req = exports.json = exports.toBuffer = void 0;\nconst http = __importStar(__webpack_require__(/*! http */ \"http\"));\nconst https = __importStar(__webpack_require__(/*! https */ \"https\"));\nasync function toBuffer(stream) {\n    let length = 0;\n    const chunks = [];\n    for await (const chunk of stream) {\n        length += chunk.length;\n        chunks.push(chunk);\n    }\n    return Buffer.concat(chunks, length);\n}\nexports.toBuffer = toBuffer;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nasync function json(stream) {\n    const buf = await toBuffer(stream);\n    const str = buf.toString('utf8');\n    try {\n        return JSON.parse(str);\n    }\n    catch (_err) {\n        const err = _err;\n        err.message += ` (input: ${str})`;\n        throw err;\n    }\n}\nexports.json = json;\nfunction req(url, opts = {}) {\n    const href = typeof url === 'string' ? url : url.href;\n    const req = (href.startsWith('https:') ? https : http).request(url, opts);\n    const promise = new Promise((resolve, reject) => {\n        req\n            .once('response', resolve)\n            .once('error', reject)\n            .end();\n    });\n    req.then = promise.then.bind(promise);\n    return req;\n}\nexports.req = req;\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agent-base/dist/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agent-base/dist/index.js":
/*!***********************************************!*\
  !*** ./node_modules/agent-base/dist/index.js ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Agent = void 0;\nconst net = __importStar(__webpack_require__(/*! net */ \"net\"));\nconst http = __importStar(__webpack_require__(/*! http */ \"http\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\n__exportStar(__webpack_require__(/*! ./helpers */ \"(rsc)/./node_modules/agent-base/dist/helpers.js\"), exports);\nconst INTERNAL = Symbol('AgentBaseInternalState');\nclass Agent extends http.Agent {\n    constructor(opts) {\n        super(opts);\n        this[INTERNAL] = {};\n    }\n    /**\n     * Determine whether this is an `http` or `https` request.\n     */\n    isSecureEndpoint(options) {\n        if (options) {\n            // First check the `secureEndpoint` property explicitly, since this\n            // means that a parent `Agent` is \"passing through\" to this instance.\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            if (typeof options.secureEndpoint === 'boolean') {\n                return options.secureEndpoint;\n            }\n            // If no explicit `secure` endpoint, check if `protocol` property is\n            // set. This will usually be the case since using a full string URL\n            // or `URL` instance should be the most common usage.\n            if (typeof options.protocol === 'string') {\n                return options.protocol === 'https:';\n            }\n        }\n        // Finally, if no `protocol` property was set, then fall back to\n        // checking the stack trace of the current call stack, and try to\n        // detect the \"https\" module.\n        const { stack } = new Error();\n        if (typeof stack !== 'string')\n            return false;\n        return stack\n            .split('\\n')\n            .some((l) => l.indexOf('(https.js:') !== -1 ||\n            l.indexOf('node:https:') !== -1);\n    }\n    // In order to support async signatures in `connect()` and Node's native\n    // connection pooling in `http.Agent`, the array of sockets for each origin\n    // has to be updated synchronously. This is so the length of the array is\n    // accurate when `addRequest()` is next called. We achieve this by creating a\n    // fake socket and adding it to `sockets[origin]` and incrementing\n    // `totalSocketCount`.\n    incrementSockets(name) {\n        // If `maxSockets` and `maxTotalSockets` are both Infinity then there is no\n        // need to create a fake socket because Node.js native connection pooling\n        // will never be invoked.\n        if (this.maxSockets === Infinity && this.maxTotalSockets === Infinity) {\n            return null;\n        }\n        // All instances of `sockets` are expected TypeScript errors. The\n        // alternative is to add it as a private property of this class but that\n        // will break TypeScript subclassing.\n        if (!this.sockets[name]) {\n            // @ts-expect-error `sockets` is readonly in `@types/node`\n            this.sockets[name] = [];\n        }\n        const fakeSocket = new net.Socket({ writable: false });\n        this.sockets[name].push(fakeSocket);\n        // @ts-expect-error `totalSocketCount` isn't defined in `@types/node`\n        this.totalSocketCount++;\n        return fakeSocket;\n    }\n    decrementSockets(name, socket) {\n        if (!this.sockets[name] || socket === null) {\n            return;\n        }\n        const sockets = this.sockets[name];\n        const index = sockets.indexOf(socket);\n        if (index !== -1) {\n            sockets.splice(index, 1);\n            // @ts-expect-error  `totalSocketCount` isn't defined in `@types/node`\n            this.totalSocketCount--;\n            if (sockets.length === 0) {\n                // @ts-expect-error `sockets` is readonly in `@types/node`\n                delete this.sockets[name];\n            }\n        }\n    }\n    // In order to properly update the socket pool, we need to call `getName()` on\n    // the core `https.Agent` if it is a secureEndpoint.\n    getName(options) {\n        const secureEndpoint = this.isSecureEndpoint(options);\n        if (secureEndpoint) {\n            // @ts-expect-error `getName()` isn't defined in `@types/node`\n            return https_1.Agent.prototype.getName.call(this, options);\n        }\n        // @ts-expect-error `getName()` isn't defined in `@types/node`\n        return super.getName(options);\n    }\n    createSocket(req, options, cb) {\n        const connectOpts = {\n            ...options,\n            secureEndpoint: this.isSecureEndpoint(options),\n        };\n        const name = this.getName(connectOpts);\n        const fakeSocket = this.incrementSockets(name);\n        Promise.resolve()\n            .then(() => this.connect(req, connectOpts))\n            .then((socket) => {\n            this.decrementSockets(name, fakeSocket);\n            if (socket instanceof http.Agent) {\n                try {\n                    // @ts-expect-error `addRequest()` isn't defined in `@types/node`\n                    return socket.addRequest(req, connectOpts);\n                }\n                catch (err) {\n                    return cb(err);\n                }\n            }\n            this[INTERNAL].currentSocket = socket;\n            // @ts-expect-error `createSocket()` isn't defined in `@types/node`\n            super.createSocket(req, options, cb);\n        }, (err) => {\n            this.decrementSockets(name, fakeSocket);\n            cb(err);\n        });\n    }\n    createConnection() {\n        const socket = this[INTERNAL].currentSocket;\n        this[INTERNAL].currentSocket = undefined;\n        if (!socket) {\n            throw new Error('No socket was returned in the `connect()` function');\n        }\n        return socket;\n    }\n    get defaultPort() {\n        return (this[INTERNAL].defaultPort ??\n            (this.protocol === 'https:' ? 443 : 80));\n    }\n    set defaultPort(v) {\n        if (this[INTERNAL]) {\n            this[INTERNAL].defaultPort = v;\n        }\n    }\n    get protocol() {\n        return (this[INTERNAL].protocol ??\n            (this.isSecureEndpoint() ? 'https:' : 'http:'));\n    }\n    set protocol(v) {\n        if (this[INTERNAL]) {\n            this[INTERNAL].protocol = v;\n        }\n    }\n}\nexports.Agent = Agent;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agent-base/dist/index.js\n");

/***/ })

};
;