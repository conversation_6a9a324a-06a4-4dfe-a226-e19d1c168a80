{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SOUxVonkr7n/ajPn40Jnr36zkyHIU/gS5nWoufTpDcA=", "__NEXT_PREVIEW_MODE_ID": "f7f36fc91a273818bb6f3964cbeeb16d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "74bbce68097376609e9a74a55af2ff0ffb54e51eca9c177a8da9d4543b044ecc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7939fa64e8236134fafe0781918f5d3224e00c76b7eb98522d4df0b037dcf1b5"}}}, "functions": {}, "sortedMiddleware": ["/"]}