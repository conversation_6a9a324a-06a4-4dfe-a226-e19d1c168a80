{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SOUxVonkr7n/ajPn40Jnr36zkyHIU/gS5nWoufTpDcA=", "__NEXT_PREVIEW_MODE_ID": "41055aff36c351125f45df8623a51b48", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ad51e78d8c948d81975159815faa37b50c495c67bb12fa95d5fc667af444be5a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "374339ab3d71d8bff7c2ceb166838fd7ef1d6b928661b3eea4605a51ad3d45b3"}}}, "functions": {}, "sortedMiddleware": ["/"]}