{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SOUxVonkr7n/ajPn40Jnr36zkyHIU/gS5nWoufTpDcA=", "__NEXT_PREVIEW_MODE_ID": "c6975a5346f283027d07fd72248391f5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "60e477b08d962134a6047b8ec8de1f4801bc0d6feb11554a6862a556fa683376", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a63e512e7719ae22700bc5e679000ed4ef56e9192768072971f69594645accca"}}}, "functions": {}, "sortedMiddleware": ["/"]}