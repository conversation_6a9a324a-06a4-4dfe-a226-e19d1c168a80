{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "GOOGLE_CLIENT_ID": "@google_client_id", "GOOGLE_CLIENT_SECRET": "@google_client_secret", "GOOGLE_SERVICE_ACCOUNT_EMAIL": "@google_service_account_email", "GOOGLE_PRIVATE_KEY": "@google_private_key", "GOOGLE_DRIVE_FOLDER_ID": "@google_drive_folder_id", "GOOGLE_SHEETS_ID": "@google_sheets_id"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}