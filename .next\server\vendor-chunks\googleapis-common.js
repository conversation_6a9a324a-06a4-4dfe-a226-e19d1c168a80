"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/googleapis-common";
exports.ids = ["vendor-chunks/googleapis-common"];
exports.modules = {

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apiIndex.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getAPI = getAPI;\nfunction getAPI(api, options, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nversions, context) {\n    let version;\n    if (typeof options === 'string') {\n        version = options;\n        options = {};\n    }\n    else if (typeof options === 'object') {\n        version = options.version;\n        delete options.version;\n    }\n    else {\n        throw new Error('Argument error: Accepts only string or object');\n    }\n    try {\n        const ctr = versions[version];\n        const ep = new ctr(options, context);\n        return Object.freeze(ep);\n    }\n    catch (e) {\n        throw new Error(`Unable to load endpoint ${api}(\"${version}\"): ${e.message}`);\n    }\n}\n//# sourceMappingURL=apiIndex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apirequest.js":
/*!****************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apirequest.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createAPIRequest = createAPIRequest;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst urlTemplate = __webpack_require__(/*! url-template */ \"(rsc)/./node_modules/url-template/lib/url-template.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst isbrowser_1 = __webpack_require__(/*! ./isbrowser */ \"(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\");\nconst h2 = __webpack_require__(/*! ./http2 */ \"(rsc)/./node_modules/googleapis-common/build/src/http2.js\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/googleapis-common/build/src/util.js\");\n// eslint-disable-next-line @typescript-eslint/no-var-requires\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/googleapis-common/package.json\");\nconst randomUUID = () => globalThis.crypto?.randomUUID() || (__webpack_require__(/*! crypto */ \"crypto\").randomUUID)();\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction isReadableStream(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        typeof obj.pipe === 'function' &&\n        obj.readable !== false &&\n        typeof obj._read === 'function' &&\n        typeof obj._readableState === 'object');\n}\nfunction getMissingParams(params, required) {\n    const missing = new Array();\n    required.forEach(param => {\n        // Is the required param in the params object?\n        if (params[param] === undefined) {\n            missing.push(param);\n        }\n    });\n    // If there are any required params missing, return their names in array,\n    // otherwise return null\n    return missing.length > 0 ? missing : null;\n}\nfunction createAPIRequest(parameters, callback) {\n    if (callback) {\n        createAPIRequestAsync(parameters).then(r => callback(null, r), callback);\n    }\n    else {\n        return createAPIRequestAsync(parameters);\n    }\n}\nasync function createAPIRequestAsync(parameters) {\n    // Combine the GaxiosOptions options passed with this specific\n    // API call with the global options configured at the API Context\n    // level, or at the global level.\n    const options = extend(true, {}, // Ensure we don't leak settings upstream\n    parameters.context.google?._options || {}, // Google level options\n    parameters.context._options || {}, // Per-API options\n    parameters.options);\n    const params = extend(true, {}, // New base object\n    options.params, // Combined global/per-api params\n    parameters.params);\n    options.userAgentDirectives = options.userAgentDirectives || [];\n    const media = params.media || {};\n    /**\n     * In a previous version of this API, the request body was stuffed in a field\n     * named `resource`.  This caused lots of problems, because it's not uncommon\n     * to have an actual named parameter required which is also named `resource`.\n     * This meant that users would have to use `resource_` in those cases, which\n     * pretty much nobody figures out on their own. The request body is now\n     * documented as being in the `requestBody` property, but we also need to keep\n     * using `resource` for reasons of back-compat. Cases that need to be covered\n     * here:\n     * - user provides just a `resource` with a request body\n     * - user provides both a `resource` and a `resource_`\n     * - user provides just a `requestBody`\n     * - user provides both a `requestBody` and a `resource`\n     */\n    let resource = params.requestBody;\n    if (!params.requestBody &&\n        params.resource &&\n        (!parameters.requiredParams.includes('resource') ||\n            typeof params.resource !== 'string')) {\n        resource = params.resource;\n        delete params.resource;\n    }\n    delete params.requestBody;\n    let authClient = params.auth || options.auth;\n    const defaultMime = typeof media.body === 'string' ? 'text/plain' : 'application/octet-stream';\n    delete params.media;\n    delete params.auth;\n    // Grab headers from user provided options\n    const headers = (0, util_1.headersToClassicHeaders)(params.headers || {});\n    populateAPIHeader(headers, options.apiVersion);\n    delete params.headers;\n    // Un-alias parameters that were modified due to conflicts with reserved names\n    Object.keys(params).forEach(key => {\n        if (key.slice(-1) === '_') {\n            const newKey = key.slice(0, -1);\n            params[newKey] = params[key];\n            delete params[key];\n        }\n    });\n    // Check for missing required parameters in the API request\n    const missingParams = getMissingParams(params, parameters.requiredParams);\n    if (missingParams) {\n        // Some params are missing - stop further operations and inform the\n        // developer which required params are not included in the request\n        throw new Error('Missing required parameters: ' + missingParams.join(', '));\n    }\n    // Parse urls\n    if (options.url) {\n        let url = options.url;\n        if (typeof url === 'object') {\n            url = url.toString();\n        }\n        options.url = urlTemplate.parse(url).expand(params);\n    }\n    if (parameters.mediaUrl) {\n        parameters.mediaUrl = urlTemplate.parse(parameters.mediaUrl).expand(params);\n    }\n    // Rewrite url if rootUrl is globally set\n    if (parameters.context._options.rootUrl !== undefined &&\n        options.url !== undefined) {\n        const originalUrl = new URL(options.url);\n        const path = originalUrl.href.substr(originalUrl.origin.length);\n        options.url = new URL(path, parameters.context._options.rootUrl).href;\n    }\n    // When forming the querystring, override the serializer so that array\n    // values are serialized like this:\n    // myParams: ['one', 'two'] ---> 'myParams=one&myParams=two'\n    // This serializer also encodes spaces in the querystring as `%20`,\n    // whereas the default serializer in gaxios encodes to a `+`.\n    options.paramsSerializer = params => {\n        return qs.stringify(params, { arrayFormat: 'repeat' });\n    };\n    // delete path params from the params object so they do not end up in query\n    parameters.pathParams.forEach(param => delete params[param]);\n    // if authClient is actually a string, use it as an API KEY\n    if (typeof authClient === 'string') {\n        params.key = params.key || authClient;\n        authClient = undefined;\n    }\n    function multipartUpload(multipart) {\n        const boundary = randomUUID();\n        const finale = `--${boundary}--`;\n        const rStream = new stream.PassThrough({\n            flush(callback) {\n                this.push('\\r\\n');\n                this.push(finale);\n                callback();\n            },\n        });\n        const pStream = new ProgressStream();\n        const isStream = isReadableStream(multipart[1].body);\n        headers['content-type'] = `multipart/related; boundary=${boundary}`;\n        for (const part of multipart) {\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part['content-type']}\\r\\n\\r\\n`;\n            rStream.push(preamble);\n            if (typeof part.body === 'string') {\n                rStream.push(part.body);\n                rStream.push('\\r\\n');\n            }\n            else {\n                // Gaxios does not natively support onUploadProgress in node.js.\n                // Pipe through the pStream first to read the number of bytes read\n                // for the purpose of tracking progress.\n                pStream.on('progress', bytesRead => {\n                    if (options.onUploadProgress) {\n                        options.onUploadProgress({ bytesRead });\n                    }\n                });\n                part.body.pipe(pStream).pipe(rStream);\n            }\n        }\n        if (!isStream) {\n            rStream.push(finale);\n            rStream.push(null);\n        }\n        options.data = rStream;\n    }\n    function browserMultipartUpload(multipart) {\n        const boundary = randomUUID();\n        const finale = `--${boundary}--`;\n        headers['content-type'] = `multipart/related; boundary=${boundary}`;\n        let content = '';\n        for (const part of multipart) {\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part['content-type']}\\r\\n\\r\\n`;\n            content += preamble;\n            if (typeof part.body === 'string') {\n                content += part.body;\n                content += '\\r\\n';\n            }\n        }\n        content += finale;\n        options.data = content;\n    }\n    if (parameters.mediaUrl && media.body) {\n        options.url = parameters.mediaUrl;\n        if (resource) {\n            params.uploadType = 'multipart';\n            const multipart = [\n                { 'content-type': 'application/json', body: JSON.stringify(resource) },\n                {\n                    'content-type': media.mimeType || (resource && resource.mimeType) || defaultMime,\n                    body: media.body,\n                },\n            ];\n            if (!(0, isbrowser_1.isBrowser)()) {\n                // gaxios doesn't support multipart/related uploads, so it has to\n                // be implemented here.\n                multipartUpload(multipart);\n            }\n            else {\n                browserMultipartUpload(multipart);\n            }\n        }\n        else {\n            params.uploadType = 'media';\n            Object.assign(headers, { 'content-type': media.mimeType || defaultMime });\n            options.data = media.body;\n        }\n    }\n    else {\n        options.data = resource || undefined;\n    }\n    options.headers = gaxios_1.Gaxios.mergeHeaders(options.headers || {}, headers);\n    options.params = params;\n    if (!(0, isbrowser_1.isBrowser)()) {\n        options.headers.set('Accept-Encoding', 'gzip');\n        options.userAgentDirectives.push({\n            product: 'google-api-nodejs-client',\n            version: pkg.version,\n            comment: 'gzip',\n        });\n        const userAgent = options.userAgentDirectives\n            .map(d => {\n            let line = `${d.product}/${d.version}`;\n            if (d.comment) {\n                line += ` (${d.comment})`;\n            }\n            return line;\n        })\n            .join(' ');\n        options.headers.set('User-Agent', userAgent);\n    }\n    // By default gaxios treats any 2xx as valid, and all non 2xx status\n    // codes as errors.  This is a problem for HTTP 304s when used along\n    // with an eTag.\n    if (!options.validateStatus) {\n        options.validateStatus = status => {\n            return (status >= 200 && status < 300) || status === 304;\n        };\n    }\n    // Retry by default\n    options.retry = options.retry === undefined ? true : options.retry;\n    delete options.auth; // is overridden by our auth code\n    // Determine TPC universe\n    if (options.universeDomain &&\n        options.universe_domain &&\n        options.universeDomain !== options.universe_domain) {\n        throw new Error('Please set either universe_domain or universeDomain, but not both.');\n    }\n    const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'\n        ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']\n        : undefined;\n    const universeDomain = options.universeDomain ??\n        options.universe_domain ??\n        universeDomainEnvVar ??\n        'googleapis.com';\n    // Update URL to point to the given TPC universe\n    if (universeDomain !== 'googleapis.com' && options.url) {\n        const url = new URL(options.url);\n        if (url.hostname.endsWith('.googleapis.com')) {\n            url.hostname = url.hostname.replace(/googleapis\\.com$/, universeDomain);\n            options.url = url.toString();\n        }\n    }\n    // An empty params would add a querystring on a spec-compliant serializer\n    if (!Object.keys(options.params).length) {\n        delete options.params;\n        delete options.paramsSerializer;\n    }\n    // Perform the HTTP request.  NOTE: this function used to return a\n    // mikeal/request object. Since the transition to Axios, the method is\n    // now void.  This may be a source of confusion for users upgrading from\n    // version 24.0 -> 25.0 or up.\n    if (authClient && typeof authClient === 'object') {\n        // Validate TPC universe\n        const universeFromAuth = typeof authClient.getUniverseDomain === 'function'\n            ? await authClient.getUniverseDomain()\n            : undefined;\n        if (universeFromAuth && universeDomain !== universeFromAuth) {\n            throw new Error(`The configured universe domain (${universeDomain}) does not match the universe domain found in the credentials (${universeFromAuth}). ` +\n                \"If you haven't configured the universe domain explicitly, googleapis.com is the default.\");\n        }\n        if (options.http2) {\n            const authHeaders = await authClient.getRequestHeaders(options.url);\n            const mooOpts = Object.assign({}, options);\n            mooOpts.headers = gaxios_1.Gaxios.mergeHeaders(mooOpts.headers, authHeaders);\n            return h2.request(mooOpts);\n        }\n        else {\n            const res = await authClient.request(options);\n            return (0, util_1.marshallGaxiosResponse)(res);\n        }\n    }\n    else {\n        return new gaxios_1.Gaxios()\n            .request(options)\n            .then(res => (0, util_1.marshallGaxiosResponse)(res));\n    }\n}\n/**\n * Basic Passthrough Stream that records the number of bytes read\n * every time the cursor is moved.\n */\nclass ProgressStream extends stream.Transform {\n    bytesRead = 0;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _transform(chunk, encoding, callback) {\n        this.bytesRead += chunk.length;\n        this.emit('progress', this.bytesRead);\n        this.push(chunk);\n        callback();\n    }\n}\nfunction populateAPIHeader(headers, apiVersion) {\n    // TODO: we should eventually think about adding browser support for this\n    // populating the gl-web header (web support should also be added to\n    // google-auth-library-nodejs).\n    if (!(0, isbrowser_1.isBrowser)()) {\n        headers['x-goog-api-client'] =\n            `gdcl/${pkg.version} gl-node/${process.versions.node}`;\n    }\n    if (apiVersion) {\n        headers['x-goog-api-version'] = apiVersion;\n    }\n}\n//# sourceMappingURL=apirequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/authplus.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/authplus.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AuthPlus = void 0;\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nclass AuthPlus extends google_auth_library_1.GoogleAuth {\n    JWT = google_auth_library_1.JWT;\n    Compute = google_auth_library_1.Compute;\n    OAuth2 = google_auth_library_1.OAuth2Client;\n    GoogleAuth = google_auth_library_1.GoogleAuth;\n    AwsClient = google_auth_library_1.AwsClient;\n    IdentityPoolClient = google_auth_library_1.IdentityPoolClient;\n    ExternalAccountClient = google_auth_library_1.ExternalAccountClient;\n    _cachedAuth;\n    /**\n     * Override getClient(), memoizing an instance of auth for\n     * subsequent calls to getProjectId().\n     */\n    async getClient(options) {\n        this._cachedAuth = new google_auth_library_1.GoogleAuth(options);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return this._cachedAuth.getClient();\n    }\n    getProjectId(callback) {\n        if (callback) {\n            return this._cachedAuth\n                ? this._cachedAuth.getProjectId(callback)\n                : super.getProjectId(callback);\n        }\n        else {\n            return this._cachedAuth\n                ? this._cachedAuth.getProjectId()\n                : super.getProjectId();\n        }\n    }\n}\nexports.AuthPlus = AuthPlus;\n//# sourceMappingURL=authplus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/authplus.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/discovery.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/discovery.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Discovery = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nconst resolve = __webpack_require__(/*! url */ \"url\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nconst readFile = util.promisify(fs.readFile);\nclass Discovery {\n    transporter = new gaxios_1.Gaxios();\n    options;\n    /**\n     * Discovery for discovering API endpoints\n     *\n     * @param options Options for discovery\n     */\n    constructor(options) {\n        this.options = options || {};\n    }\n    /**\n     * Generate and Endpoint from an endpoint schema object.\n     *\n     * @param schema The schema from which to generate the Endpoint.\n     * @return A function that creates an endpoint.\n     */\n    makeEndpoint(schema) {\n        return (options) => {\n            const ep = new endpoint_1.Endpoint(options);\n            ep.applySchema(ep, schema, schema, ep);\n            return ep;\n        };\n    }\n    /**\n     * Log output of generator. Works just like console.log\n     */\n    log(...args) {\n        if (this.options && this.options.debug) {\n            console.log(...args);\n        }\n    }\n    /**\n     * Generate all APIs and return as in-memory object.\n     * @param discoveryUrl\n     */\n    async discoverAllAPIs(discoveryUrl) {\n        const headers = new Headers(this.options.includePrivate ? {} : { 'X-User-Ip': '0.0.0.0' });\n        const res = await this.transporter.request({\n            url: discoveryUrl,\n            headers,\n        });\n        const items = res.data.items;\n        const apis = await Promise.all(items.map(async (api) => {\n            const endpointCreator = await this.discoverAPI(api.discoveryRestUrl);\n            return { api, endpointCreator };\n        }));\n        const versionIndex = {};\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const apisIndex = {};\n        for (const set of apis) {\n            if (!apisIndex[set.api.name]) {\n                versionIndex[set.api.name] = {};\n                apisIndex[set.api.name] = (options) => {\n                    const type = typeof options;\n                    let version;\n                    if (type === 'string') {\n                        version = options;\n                        options = {};\n                    }\n                    else if (type === 'object') {\n                        version = options.version;\n                        delete options.version;\n                    }\n                    else {\n                        throw new Error('Argument error: Accepts only string or object');\n                    }\n                    try {\n                        const ep = set.endpointCreator(options, this);\n                        return Object.freeze(ep); // create new & freeze\n                    }\n                    catch (e) {\n                        throw new Error(util.format('Unable to load endpoint %s(\"%s\"): %s', set.api.name, version, e.message));\n                    }\n                };\n            }\n            versionIndex[set.api.name][set.api.version] = set.endpointCreator;\n        }\n        return apisIndex;\n    }\n    /**\n     * Generate API file given discovery URL\n     *\n     * @param apiDiscoveryUrl URL or filename of discovery doc for API\n     * @returns A promise that resolves with a function that creates the endpoint\n     */\n    async discoverAPI(apiDiscoveryUrl) {\n        if (typeof apiDiscoveryUrl === 'string') {\n            const parts = resolve.parse(apiDiscoveryUrl);\n            if (apiDiscoveryUrl && !parts.protocol) {\n                this.log('Reading from file ' + apiDiscoveryUrl);\n                const file = await readFile(apiDiscoveryUrl, { encoding: 'utf8' });\n                return this.makeEndpoint(JSON.parse(file));\n            }\n            else {\n                this.log('Requesting ' + apiDiscoveryUrl);\n                const res = await this.transporter.request({\n                    url: apiDiscoveryUrl,\n                });\n                return this.makeEndpoint(res.data);\n            }\n        }\n        else {\n            const options = apiDiscoveryUrl;\n            this.log('Requesting ' + options.url);\n            const url = options.url;\n            delete options.url;\n            const parameters = {\n                options: { url, method: 'GET' },\n                requiredParams: [],\n                pathParams: [],\n                params: options,\n                context: { google: { _options: {} }, _options: {} },\n            };\n            const res = await (0, apirequest_1.createAPIRequest)(parameters);\n            return this.makeEndpoint(res.data);\n        }\n    }\n}\nexports.Discovery = Discovery;\n//# sourceMappingURL=discovery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/discovery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/endpoint.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/endpoint.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Endpoint = void 0;\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nclass Endpoint {\n    _options;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    google;\n    constructor(options) {\n        this._options = options || {};\n    }\n    /**\n     * Given a schema, add methods and resources to a target.\n     *\n     * @param {object} target The target to which to apply the schema.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods and\n     * resources.\n     * @param {object} context The context to add to each method.\n     */\n    applySchema(target, rootSchema, schema, context) {\n        this.applyMethodsFromSchema(target, rootSchema, schema, context);\n        if (schema.resources) {\n            for (const resourceName in schema.resources) {\n                if (Object.prototype.hasOwnProperty.call(schema.resources, resourceName)) {\n                    const resource = schema.resources[resourceName];\n                    if (!target[resourceName]) {\n                        target[resourceName] = {};\n                    }\n                    this.applySchema(target[resourceName], rootSchema, resource, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a schema, add methods to a target.\n     *\n     * @param {object} target The target to which to apply the methods.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods.\n     * @param {object} context The context to add to each method.\n     */\n    applyMethodsFromSchema(target, rootSchema, schema, context) {\n        if (schema.methods) {\n            for (const name in schema.methods) {\n                if (Object.prototype.hasOwnProperty.call(schema.methods, name)) {\n                    const method = schema.methods[name];\n                    target[name] = this.makeMethod(rootSchema, method, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a method schema, add a method to a target.\n     *\n     * @param target The target to which to add the method.\n     * @param schema The top-level schema that contains the rootUrl, etc.\n     * @param method The method schema from which to generate the method.\n     * @param context The context to add to the method.\n     */\n    makeMethod(schema, method, context) {\n        return (paramsOrCallback, callback) => {\n            const params = typeof paramsOrCallback === 'function' ? {} : paramsOrCallback;\n            callback =\n                typeof paramsOrCallback === 'function'\n                    ? paramsOrCallback\n                    : callback;\n            const schemaUrl = buildurl(schema.rootUrl + schema.servicePath + method.path);\n            const parameters = {\n                options: {\n                    url: schemaUrl.substring(1, schemaUrl.length - 1),\n                    method: method.httpMethod,\n                    apiVersion: method.apiVersion,\n                },\n                params,\n                requiredParams: method.parameterOrder || [],\n                pathParams: this.getPathParams(method.parameters),\n                context,\n            };\n            if (method.mediaUpload &&\n                method.mediaUpload.protocols &&\n                method.mediaUpload.protocols.simple &&\n                method.mediaUpload.protocols.simple.path) {\n                const mediaUrl = buildurl(schema.rootUrl + method.mediaUpload.protocols.simple.path);\n                parameters.mediaUrl = mediaUrl.substring(1, mediaUrl.length - 1);\n            }\n            if (!callback) {\n                return (0, apirequest_1.createAPIRequest)(parameters);\n            }\n            (0, apirequest_1.createAPIRequest)(parameters, callback);\n            return;\n        };\n    }\n    getPathParams(params) {\n        const pathParams = new Array();\n        if (typeof params !== 'object') {\n            params = {};\n        }\n        Object.keys(params).forEach(key => {\n            if (params[key].location === 'path') {\n                pathParams.push(key);\n            }\n        });\n        return pathParams;\n    }\n}\nexports.Endpoint = Endpoint;\n/**\n * Build a string used to create a URL from the discovery doc provided URL.\n * replace double slashes with single slash (except in https://)\n * @private\n * @param  input URL to build from\n * @return Resulting built URL\n */\nfunction buildurl(input) {\n    return input ? `'${input}'`.replace(/([^:]\\/)\\/+/g, '$1') : '';\n}\n//# sourceMappingURL=endpoint.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/http2.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/http2.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sessions = void 0;\nexports.request = request;\nexports.closeSession = closeSession;\nconst http2 = __webpack_require__(/*! http2 */ \"http2\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst process = __webpack_require__(/*! process */ \"process\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/googleapis-common/build/src/util.js\");\nconst { HTTP2_HEADER_CONTENT_ENCODING, HTTP2_HEADER_CONTENT_TYPE, HTTP2_HEADER_METHOD, HTTP2_HEADER_PATH, HTTP2_HEADER_STATUS, } = http2.constants;\nconst DEBUG = !!process.env.HTTP2_DEBUG;\n/**\n * List of sessions current in use.\n * @private\n */\nexports.sessions = {};\n/**\n * Public method to make an http2 request.\n * @param config - Request options.\n */\nasync function request(config) {\n    const opts = extend(true, {}, config);\n    opts.validateStatus = opts.validateStatus || validateStatus;\n    opts.responseType = opts.responseType || 'json';\n    const url = new url_1.URL(opts.url);\n    // Check for an existing session to this host, or go create a new one.\n    const sessionData = _getClient(url.host);\n    // Since we're using this session, clear the timeout handle to ensure\n    // it stays in memory and connected for a while further.\n    if (sessionData.timeoutHandle !== undefined) {\n        clearTimeout(sessionData.timeoutHandle);\n    }\n    // Assemble the querystring based on config.params.  We're using the\n    // `qs` module to make life a little easier.\n    let pathWithQs = url.pathname;\n    if (config.params && Object.keys(config.params).length > 0) {\n        const serializer = config.paramsSerializer || qs.stringify;\n        const q = serializer(opts.params);\n        pathWithQs += `?${q}`;\n    }\n    // Assemble the headers based on basic HTTP2 primitives (path, method) and\n    // custom headers sent from the consumer. Note: the native `Headers` type does\n    // not support HTTP2 header names (e.g. ':status')\n    const headers = (0, util_1.headersToClassicHeaders)(opts.headers);\n    headers[HTTP2_HEADER_PATH] = pathWithQs;\n    headers[HTTP2_HEADER_METHOD] = config.method || 'GET';\n    opts.headers = headers;\n    // NOTE: This is working around an upstream bug in `apirequest.ts`. The\n    // request path assumes that the `content-type` header is going to be set in\n    // the underlying HTTP Client. This hack provides bug for bug compatability\n    // with this bug in gaxios:\n    // https://github.com/googleapis/gaxios/blob/main/src/gaxios.ts#L202\n    if (!headers[HTTP2_HEADER_CONTENT_TYPE]) {\n        if (opts.responseType !== 'text') {\n            headers[HTTP2_HEADER_CONTENT_TYPE] = 'application/json';\n        }\n    }\n    const res = {\n        config,\n        headers: {},\n        status: 0,\n        data: {},\n        statusText: '',\n    };\n    const chunks = [];\n    const session = sessionData.session;\n    let req;\n    return new Promise((resolve, reject) => {\n        try {\n            req = session\n                .request(headers)\n                .on('response', responseHeaders => {\n                Object.assign(res, {\n                    headers: responseHeaders,\n                    status: responseHeaders[HTTP2_HEADER_STATUS],\n                });\n                let stream = req;\n                if (responseHeaders[HTTP2_HEADER_CONTENT_ENCODING] === 'gzip') {\n                    stream = req.pipe(zlib.createGunzip());\n                }\n                if (opts.responseType === 'stream') {\n                    res.data = stream;\n                    resolve(res);\n                    return;\n                }\n                stream\n                    .on('data', d => {\n                    chunks.push(d);\n                })\n                    .on('error', err => {\n                    reject(err);\n                    return;\n                })\n                    .on('end', () => {\n                    const buf = Buffer.concat(chunks);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    let data = buf;\n                    if (buf) {\n                        if (opts.responseType === 'json') {\n                            try {\n                                data = JSON.parse(buf.toString('utf8'));\n                            }\n                            catch {\n                                data = buf.toString('utf8');\n                            }\n                        }\n                        else if (opts.responseType === 'text') {\n                            data = buf.toString('utf8');\n                        }\n                        else if (opts.responseType === 'arraybuffer') {\n                            data = buf.buffer;\n                        }\n                        res.data = data;\n                    }\n                    if (!opts.validateStatus(res.status)) {\n                        let message = `Request failed with status code ${res.status}. `;\n                        if (res.data && typeof res.data === 'object') {\n                            const body = util.inspect(res.data, { depth: 5 });\n                            message = `${message}\\n'${body}`;\n                        }\n                        reject(new Error(message, { cause: res }));\n                    }\n                    resolve(res);\n                    return;\n                });\n            })\n                .on('error', e => {\n                reject(e);\n                return;\n            });\n        }\n        catch (e) {\n            closeSession(url)\n                .then(() => reject(e))\n                .catch(reject);\n            return;\n        }\n        res.request = req;\n        // If data was provided, write it to the request in the form of\n        // a stream, string data, or a basic object.\n        if (config.data) {\n            if (config.data instanceof stream_1.Stream) {\n                config.data.pipe(req);\n            }\n            else if (typeof config.data === 'string') {\n                const data = Buffer.from(config.data);\n                req.end(data);\n            }\n            else if (typeof config.data === 'object') {\n                const data = JSON.stringify(config.data);\n                req.end(data);\n            }\n        }\n        // Create a timeout so the Http2Session will be cleaned up after\n        // a period of non-use. 500 milliseconds was chosen because it's\n        // a nice round number, and I don't know what would be a better\n        // choice. Keeping this channel open will hold a file descriptor\n        // which will prevent the process from exiting.\n        sessionData.timeoutHandle = setTimeout(() => closeSession(url), 500);\n    });\n}\n/**\n * By default, throw for any non-2xx status code\n * @param status - status code from the HTTP response\n */\nfunction validateStatus(status) {\n    return status >= 200 && status < 300;\n}\n/**\n * Obtain an existing h2 session or go create a new one.\n * @param host - The hostname to which the session belongs.\n */\nfunction _getClient(host) {\n    if (!exports.sessions[host]) {\n        if (DEBUG) {\n            console.log(`Creating client for ${host}`);\n        }\n        const session = http2.connect(`https://${host}`);\n        session\n            .on('error', e => {\n            console.error(`*ERROR*: ${e}`);\n            delete exports.sessions[host];\n        })\n            .on('goaway', (errorCode, lastStreamId) => {\n            console.error(`*GOAWAY*: ${errorCode} : ${lastStreamId}`);\n            delete exports.sessions[host];\n        });\n        exports.sessions[host] = { session };\n    }\n    else {\n        if (DEBUG) {\n            console.log(`Used cached client for ${host}`);\n        }\n    }\n    return exports.sessions[host];\n}\nasync function closeSession(url) {\n    const sessionData = exports.sessions[url.host];\n    if (!sessionData) {\n        return;\n    }\n    const { session } = sessionData;\n    delete exports.sessions[url.host];\n    if (DEBUG) {\n        console.error(`Closing ${url.host}`);\n    }\n    session.close(() => {\n        if (DEBUG) {\n            console.error(`Closed ${url.host}`);\n        }\n    });\n    setTimeout(() => {\n        if (session && !session.destroyed) {\n            if (DEBUG) {\n                console.log(`Forcing close ${url.host}`);\n            }\n            if (session) {\n                session.destroy();\n            }\n        }\n    }, 1000);\n}\n//# sourceMappingURL=http2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29vZ2xlYXBpcy1jb21tb24vYnVpbGQvc3JjL2h0dHAyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdCQUFnQjtBQUNoQixlQUFlO0FBQ2Ysb0JBQW9CO0FBQ3BCLGNBQWMsbUJBQU8sQ0FBQyxvQkFBTztBQUM3QixhQUFhLG1CQUFPLENBQUMsa0JBQU07QUFDM0IsY0FBYyxtQkFBTyxDQUFDLGdCQUFLO0FBQzNCLFdBQVcsbUJBQU8sQ0FBQyxnREFBSTtBQUN2QixlQUFlLG1CQUFPLENBQUMsb0RBQVE7QUFDL0IsaUJBQWlCLG1CQUFPLENBQUMsc0JBQVE7QUFDakMsYUFBYSxtQkFBTyxDQUFDLGtCQUFNO0FBQzNCLGdCQUFnQixtQkFBTyxDQUFDLHdCQUFTO0FBQ2pDLGVBQWUsbUJBQU8sQ0FBQyx3RUFBUTtBQUMvQixRQUFRLHlIQUF5SDtBQUNqSTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsRUFBRTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5RUFBeUUsV0FBVztBQUNwRjtBQUNBLGtFQUFrRSxVQUFVO0FBQzVFLHlDQUF5QyxRQUFRLEtBQUssS0FBSztBQUMzRDtBQUNBLG9EQUFvRCxZQUFZO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLEtBQUs7QUFDcEQ7QUFDQSxpREFBaUQsS0FBSztBQUN0RDtBQUNBO0FBQ0Esc0NBQXNDLEVBQUU7QUFDeEM7QUFDQSxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsV0FBVyxJQUFJLGFBQWE7QUFDbkU7QUFDQSxTQUFTO0FBQ1QsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxLQUFLO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksVUFBVTtBQUN0QjtBQUNBO0FBQ0EsaUNBQWlDLFNBQVM7QUFDMUM7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLFNBQVM7QUFDN0M7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLFNBQVM7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpheWtlXFxEb2N1bWVudHNcXFNvdXJjZSBDb2RlXFxzaWduXFxub2RlX21vZHVsZXNcXGdvb2dsZWFwaXMtY29tbW9uXFxidWlsZFxcc3JjXFxodHRwMi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIENvcHlyaWdodCAyMDIwIEdvb2dsZSBMTENcbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4vLyB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4vLyBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbi8vXG4vLyAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5zZXNzaW9ucyA9IHZvaWQgMDtcbmV4cG9ydHMucmVxdWVzdCA9IHJlcXVlc3Q7XG5leHBvcnRzLmNsb3NlU2Vzc2lvbiA9IGNsb3NlU2Vzc2lvbjtcbmNvbnN0IGh0dHAyID0gcmVxdWlyZShcImh0dHAyXCIpO1xuY29uc3QgemxpYiA9IHJlcXVpcmUoXCJ6bGliXCIpO1xuY29uc3QgdXJsXzEgPSByZXF1aXJlKFwidXJsXCIpO1xuY29uc3QgcXMgPSByZXF1aXJlKFwicXNcIik7XG5jb25zdCBleHRlbmQgPSByZXF1aXJlKFwiZXh0ZW5kXCIpO1xuY29uc3Qgc3RyZWFtXzEgPSByZXF1aXJlKFwic3RyZWFtXCIpO1xuY29uc3QgdXRpbCA9IHJlcXVpcmUoXCJ1dGlsXCIpO1xuY29uc3QgcHJvY2VzcyA9IHJlcXVpcmUoXCJwcm9jZXNzXCIpO1xuY29uc3QgdXRpbF8xID0gcmVxdWlyZShcIi4vdXRpbFwiKTtcbmNvbnN0IHsgSFRUUDJfSEVBREVSX0NPTlRFTlRfRU5DT0RJTkcsIEhUVFAyX0hFQURFUl9DT05URU5UX1RZUEUsIEhUVFAyX0hFQURFUl9NRVRIT0QsIEhUVFAyX0hFQURFUl9QQVRILCBIVFRQMl9IRUFERVJfU1RBVFVTLCB9ID0gaHR0cDIuY29uc3RhbnRzO1xuY29uc3QgREVCVUcgPSAhIXByb2Nlc3MuZW52LkhUVFAyX0RFQlVHO1xuLyoqXG4gKiBMaXN0IG9mIHNlc3Npb25zIGN1cnJlbnQgaW4gdXNlLlxuICogQHByaXZhdGVcbiAqL1xuZXhwb3J0cy5zZXNzaW9ucyA9IHt9O1xuLyoqXG4gKiBQdWJsaWMgbWV0aG9kIHRvIG1ha2UgYW4gaHR0cDIgcmVxdWVzdC5cbiAqIEBwYXJhbSBjb25maWcgLSBSZXF1ZXN0IG9wdGlvbnMuXG4gKi9cbmFzeW5jIGZ1bmN0aW9uIHJlcXVlc3QoY29uZmlnKSB7XG4gICAgY29uc3Qgb3B0cyA9IGV4dGVuZCh0cnVlLCB7fSwgY29uZmlnKTtcbiAgICBvcHRzLnZhbGlkYXRlU3RhdHVzID0gb3B0cy52YWxpZGF0ZVN0YXR1cyB8fCB2YWxpZGF0ZVN0YXR1cztcbiAgICBvcHRzLnJlc3BvbnNlVHlwZSA9IG9wdHMucmVzcG9uc2VUeXBlIHx8ICdqc29uJztcbiAgICBjb25zdCB1cmwgPSBuZXcgdXJsXzEuVVJMKG9wdHMudXJsKTtcbiAgICAvLyBDaGVjayBmb3IgYW4gZXhpc3Rpbmcgc2Vzc2lvbiB0byB0aGlzIGhvc3QsIG9yIGdvIGNyZWF0ZSBhIG5ldyBvbmUuXG4gICAgY29uc3Qgc2Vzc2lvbkRhdGEgPSBfZ2V0Q2xpZW50KHVybC5ob3N0KTtcbiAgICAvLyBTaW5jZSB3ZSdyZSB1c2luZyB0aGlzIHNlc3Npb24sIGNsZWFyIHRoZSB0aW1lb3V0IGhhbmRsZSB0byBlbnN1cmVcbiAgICAvLyBpdCBzdGF5cyBpbiBtZW1vcnkgYW5kIGNvbm5lY3RlZCBmb3IgYSB3aGlsZSBmdXJ0aGVyLlxuICAgIGlmIChzZXNzaW9uRGF0YS50aW1lb3V0SGFuZGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHNlc3Npb25EYXRhLnRpbWVvdXRIYW5kbGUpO1xuICAgIH1cbiAgICAvLyBBc3NlbWJsZSB0aGUgcXVlcnlzdHJpbmcgYmFzZWQgb24gY29uZmlnLnBhcmFtcy4gIFdlJ3JlIHVzaW5nIHRoZVxuICAgIC8vIGBxc2AgbW9kdWxlIHRvIG1ha2UgbGlmZSBhIGxpdHRsZSBlYXNpZXIuXG4gICAgbGV0IHBhdGhXaXRoUXMgPSB1cmwucGF0aG5hbWU7XG4gICAgaWYgKGNvbmZpZy5wYXJhbXMgJiYgT2JqZWN0LmtleXMoY29uZmlnLnBhcmFtcykubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCBzZXJpYWxpemVyID0gY29uZmlnLnBhcmFtc1NlcmlhbGl6ZXIgfHwgcXMuc3RyaW5naWZ5O1xuICAgICAgICBjb25zdCBxID0gc2VyaWFsaXplcihvcHRzLnBhcmFtcyk7XG4gICAgICAgIHBhdGhXaXRoUXMgKz0gYD8ke3F9YDtcbiAgICB9XG4gICAgLy8gQXNzZW1ibGUgdGhlIGhlYWRlcnMgYmFzZWQgb24gYmFzaWMgSFRUUDIgcHJpbWl0aXZlcyAocGF0aCwgbWV0aG9kKSBhbmRcbiAgICAvLyBjdXN0b20gaGVhZGVycyBzZW50IGZyb20gdGhlIGNvbnN1bWVyLiBOb3RlOiB0aGUgbmF0aXZlIGBIZWFkZXJzYCB0eXBlIGRvZXNcbiAgICAvLyBub3Qgc3VwcG9ydCBIVFRQMiBoZWFkZXIgbmFtZXMgKGUuZy4gJzpzdGF0dXMnKVxuICAgIGNvbnN0IGhlYWRlcnMgPSAoMCwgdXRpbF8xLmhlYWRlcnNUb0NsYXNzaWNIZWFkZXJzKShvcHRzLmhlYWRlcnMpO1xuICAgIGhlYWRlcnNbSFRUUDJfSEVBREVSX1BBVEhdID0gcGF0aFdpdGhRcztcbiAgICBoZWFkZXJzW0hUVFAyX0hFQURFUl9NRVRIT0RdID0gY29uZmlnLm1ldGhvZCB8fCAnR0VUJztcbiAgICBvcHRzLmhlYWRlcnMgPSBoZWFkZXJzO1xuICAgIC8vIE5PVEU6IFRoaXMgaXMgd29ya2luZyBhcm91bmQgYW4gdXBzdHJlYW0gYnVnIGluIGBhcGlyZXF1ZXN0LnRzYC4gVGhlXG4gICAgLy8gcmVxdWVzdCBwYXRoIGFzc3VtZXMgdGhhdCB0aGUgYGNvbnRlbnQtdHlwZWAgaGVhZGVyIGlzIGdvaW5nIHRvIGJlIHNldCBpblxuICAgIC8vIHRoZSB1bmRlcmx5aW5nIEhUVFAgQ2xpZW50LiBUaGlzIGhhY2sgcHJvdmlkZXMgYnVnIGZvciBidWcgY29tcGF0YWJpbGl0eVxuICAgIC8vIHdpdGggdGhpcyBidWcgaW4gZ2F4aW9zOlxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9nb29nbGVhcGlzL2dheGlvcy9ibG9iL21haW4vc3JjL2dheGlvcy50cyNMMjAyXG4gICAgaWYgKCFoZWFkZXJzW0hUVFAyX0hFQURFUl9DT05URU5UX1RZUEVdKSB7XG4gICAgICAgIGlmIChvcHRzLnJlc3BvbnNlVHlwZSAhPT0gJ3RleHQnKSB7XG4gICAgICAgICAgICBoZWFkZXJzW0hUVFAyX0hFQURFUl9DT05URU5UX1RZUEVdID0gJ2FwcGxpY2F0aW9uL2pzb24nO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IHJlcyA9IHtcbiAgICAgICAgY29uZmlnLFxuICAgICAgICBoZWFkZXJzOiB7fSxcbiAgICAgICAgc3RhdHVzOiAwLFxuICAgICAgICBkYXRhOiB7fSxcbiAgICAgICAgc3RhdHVzVGV4dDogJycsXG4gICAgfTtcbiAgICBjb25zdCBjaHVua3MgPSBbXTtcbiAgICBjb25zdCBzZXNzaW9uID0gc2Vzc2lvbkRhdGEuc2Vzc2lvbjtcbiAgICBsZXQgcmVxO1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXEgPSBzZXNzaW9uXG4gICAgICAgICAgICAgICAgLnJlcXVlc3QoaGVhZGVycylcbiAgICAgICAgICAgICAgICAub24oJ3Jlc3BvbnNlJywgcmVzcG9uc2VIZWFkZXJzID0+IHtcbiAgICAgICAgICAgICAgICBPYmplY3QuYXNzaWduKHJlcywge1xuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzOiByZXNwb25zZUhlYWRlcnMsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czogcmVzcG9uc2VIZWFkZXJzW0hUVFAyX0hFQURFUl9TVEFUVVNdLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGxldCBzdHJlYW0gPSByZXE7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlSGVhZGVyc1tIVFRQMl9IRUFERVJfQ09OVEVOVF9FTkNPRElOR10gPT09ICdnemlwJykge1xuICAgICAgICAgICAgICAgICAgICBzdHJlYW0gPSByZXEucGlwZSh6bGliLmNyZWF0ZUd1bnppcCgpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKG9wdHMucmVzcG9uc2VUeXBlID09PSAnc3RyZWFtJykge1xuICAgICAgICAgICAgICAgICAgICByZXMuZGF0YSA9IHN0cmVhbTtcbiAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZShyZXMpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHN0cmVhbVxuICAgICAgICAgICAgICAgICAgICAub24oJ2RhdGEnLCBkID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY2h1bmtzLnB1c2goZCk7XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgLm9uKCdlcnJvcicsIGVyciA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJlamVjdChlcnIpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgLm9uKCdlbmQnLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGJ1ZiA9IEJ1ZmZlci5jb25jYXQoY2h1bmtzKTtcbiAgICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICAgICAgICAgICAgICAgICAgbGV0IGRhdGEgPSBidWY7XG4gICAgICAgICAgICAgICAgICAgIGlmIChidWYpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChvcHRzLnJlc3BvbnNlVHlwZSA9PT0gJ2pzb24nKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IEpTT04ucGFyc2UoYnVmLnRvU3RyaW5nKCd1dGY4JykpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSBidWYudG9TdHJpbmcoJ3V0ZjgnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChvcHRzLnJlc3BvbnNlVHlwZSA9PT0gJ3RleHQnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IGJ1Zi50b1N0cmluZygndXRmOCcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAob3B0cy5yZXNwb25zZVR5cGUgPT09ICdhcnJheWJ1ZmZlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gYnVmLmJ1ZmZlcjtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5kYXRhID0gZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoIW9wdHMudmFsaWRhdGVTdGF0dXMocmVzLnN0YXR1cykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBtZXNzYWdlID0gYFJlcXVlc3QgZmFpbGVkIHdpdGggc3RhdHVzIGNvZGUgJHtyZXMuc3RhdHVzfS4gYDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YSAmJiB0eXBlb2YgcmVzLmRhdGEgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYm9keSA9IHV0aWwuaW5zcGVjdChyZXMuZGF0YSwgeyBkZXB0aDogNSB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlID0gYCR7bWVzc2FnZX1cXG4nJHtib2R5fWA7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICByZWplY3QobmV3IEVycm9yKG1lc3NhZ2UsIHsgY2F1c2U6IHJlcyB9KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZShyZXMpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIC5vbignZXJyb3InLCBlID0+IHtcbiAgICAgICAgICAgICAgICByZWplY3QoZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgIGNsb3NlU2Vzc2lvbih1cmwpXG4gICAgICAgICAgICAgICAgLnRoZW4oKCkgPT4gcmVqZWN0KGUpKVxuICAgICAgICAgICAgICAgIC5jYXRjaChyZWplY3QpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHJlcy5yZXF1ZXN0ID0gcmVxO1xuICAgICAgICAvLyBJZiBkYXRhIHdhcyBwcm92aWRlZCwgd3JpdGUgaXQgdG8gdGhlIHJlcXVlc3QgaW4gdGhlIGZvcm0gb2ZcbiAgICAgICAgLy8gYSBzdHJlYW0sIHN0cmluZyBkYXRhLCBvciBhIGJhc2ljIG9iamVjdC5cbiAgICAgICAgaWYgKGNvbmZpZy5kYXRhKSB7XG4gICAgICAgICAgICBpZiAoY29uZmlnLmRhdGEgaW5zdGFuY2VvZiBzdHJlYW1fMS5TdHJlYW0pIHtcbiAgICAgICAgICAgICAgICBjb25maWcuZGF0YS5waXBlKHJlcSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0eXBlb2YgY29uZmlnLmRhdGEgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IEJ1ZmZlci5mcm9tKGNvbmZpZy5kYXRhKTtcbiAgICAgICAgICAgICAgICByZXEuZW5kKGRhdGEpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAodHlwZW9mIGNvbmZpZy5kYXRhID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnN0cmluZ2lmeShjb25maWcuZGF0YSk7XG4gICAgICAgICAgICAgICAgcmVxLmVuZChkYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvLyBDcmVhdGUgYSB0aW1lb3V0IHNvIHRoZSBIdHRwMlNlc3Npb24gd2lsbCBiZSBjbGVhbmVkIHVwIGFmdGVyXG4gICAgICAgIC8vIGEgcGVyaW9kIG9mIG5vbi11c2UuIDUwMCBtaWxsaXNlY29uZHMgd2FzIGNob3NlbiBiZWNhdXNlIGl0J3NcbiAgICAgICAgLy8gYSBuaWNlIHJvdW5kIG51bWJlciwgYW5kIEkgZG9uJ3Qga25vdyB3aGF0IHdvdWxkIGJlIGEgYmV0dGVyXG4gICAgICAgIC8vIGNob2ljZS4gS2VlcGluZyB0aGlzIGNoYW5uZWwgb3BlbiB3aWxsIGhvbGQgYSBmaWxlIGRlc2NyaXB0b3JcbiAgICAgICAgLy8gd2hpY2ggd2lsbCBwcmV2ZW50IHRoZSBwcm9jZXNzIGZyb20gZXhpdGluZy5cbiAgICAgICAgc2Vzc2lvbkRhdGEudGltZW91dEhhbmRsZSA9IHNldFRpbWVvdXQoKCkgPT4gY2xvc2VTZXNzaW9uKHVybCksIDUwMCk7XG4gICAgfSk7XG59XG4vKipcbiAqIEJ5IGRlZmF1bHQsIHRocm93IGZvciBhbnkgbm9uLTJ4eCBzdGF0dXMgY29kZVxuICogQHBhcmFtIHN0YXR1cyAtIHN0YXR1cyBjb2RlIGZyb20gdGhlIEhUVFAgcmVzcG9uc2VcbiAqL1xuZnVuY3Rpb24gdmFsaWRhdGVTdGF0dXMoc3RhdHVzKSB7XG4gICAgcmV0dXJuIHN0YXR1cyA+PSAyMDAgJiYgc3RhdHVzIDwgMzAwO1xufVxuLyoqXG4gKiBPYnRhaW4gYW4gZXhpc3RpbmcgaDIgc2Vzc2lvbiBvciBnbyBjcmVhdGUgYSBuZXcgb25lLlxuICogQHBhcmFtIGhvc3QgLSBUaGUgaG9zdG5hbWUgdG8gd2hpY2ggdGhlIHNlc3Npb24gYmVsb25ncy5cbiAqL1xuZnVuY3Rpb24gX2dldENsaWVudChob3N0KSB7XG4gICAgaWYgKCFleHBvcnRzLnNlc3Npb25zW2hvc3RdKSB7XG4gICAgICAgIGlmIChERUJVRykge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYENyZWF0aW5nIGNsaWVudCBmb3IgJHtob3N0fWApO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNlc3Npb24gPSBodHRwMi5jb25uZWN0KGBodHRwczovLyR7aG9zdH1gKTtcbiAgICAgICAgc2Vzc2lvblxuICAgICAgICAgICAgLm9uKCdlcnJvcicsIGUgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihgKkVSUk9SKjogJHtlfWApO1xuICAgICAgICAgICAgZGVsZXRlIGV4cG9ydHMuc2Vzc2lvbnNbaG9zdF07XG4gICAgICAgIH0pXG4gICAgICAgICAgICAub24oJ2dvYXdheScsIChlcnJvckNvZGUsIGxhc3RTdHJlYW1JZCkgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihgKkdPQVdBWSo6ICR7ZXJyb3JDb2RlfSA6ICR7bGFzdFN0cmVhbUlkfWApO1xuICAgICAgICAgICAgZGVsZXRlIGV4cG9ydHMuc2Vzc2lvbnNbaG9zdF07XG4gICAgICAgIH0pO1xuICAgICAgICBleHBvcnRzLnNlc3Npb25zW2hvc3RdID0geyBzZXNzaW9uIH07XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBpZiAoREVCVUcpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBVc2VkIGNhY2hlZCBjbGllbnQgZm9yICR7aG9zdH1gKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZXhwb3J0cy5zZXNzaW9uc1tob3N0XTtcbn1cbmFzeW5jIGZ1bmN0aW9uIGNsb3NlU2Vzc2lvbih1cmwpIHtcbiAgICBjb25zdCBzZXNzaW9uRGF0YSA9IGV4cG9ydHMuc2Vzc2lvbnNbdXJsLmhvc3RdO1xuICAgIGlmICghc2Vzc2lvbkRhdGEpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCB7IHNlc3Npb24gfSA9IHNlc3Npb25EYXRhO1xuICAgIGRlbGV0ZSBleHBvcnRzLnNlc3Npb25zW3VybC5ob3N0XTtcbiAgICBpZiAoREVCVUcpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihgQ2xvc2luZyAke3VybC5ob3N0fWApO1xuICAgIH1cbiAgICBzZXNzaW9uLmNsb3NlKCgpID0+IHtcbiAgICAgICAgaWYgKERFQlVHKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGBDbG9zZWQgJHt1cmwuaG9zdH1gKTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBpZiAoc2Vzc2lvbiAmJiAhc2Vzc2lvbi5kZXN0cm95ZWQpIHtcbiAgICAgICAgICAgIGlmIChERUJVRykge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBGb3JjaW5nIGNsb3NlICR7dXJsLmhvc3R9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoc2Vzc2lvbikge1xuICAgICAgICAgICAgICAgIHNlc3Npb24uZGVzdHJveSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfSwgMTAwMCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1odHRwMi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/http2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Endpoint = exports.Discovery = exports.AuthPlus = exports.createAPIRequest = exports.getAPI = exports.GaxiosError = exports.Gaxios = exports.AwsClient = exports.IdentityPoolClient = exports.BaseExternalAccountClient = exports.ExternalAccountClient = exports.GoogleAuth = exports.UserRefreshClient = exports.Compute = exports.JWT = exports.OAuth2Client = exports.gaxios = exports.googleAuthLibrary = void 0;\n// re-exporting key dependencies\nexports.googleAuthLibrary = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nexports.gaxios = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nvar google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nObject.defineProperty(exports, \"OAuth2Client\", ({ enumerable: true, get: function () { return google_auth_library_1.OAuth2Client; } }));\nObject.defineProperty(exports, \"JWT\", ({ enumerable: true, get: function () { return google_auth_library_1.JWT; } }));\nObject.defineProperty(exports, \"Compute\", ({ enumerable: true, get: function () { return google_auth_library_1.Compute; } }));\nObject.defineProperty(exports, \"UserRefreshClient\", ({ enumerable: true, get: function () { return google_auth_library_1.UserRefreshClient; } }));\nObject.defineProperty(exports, \"GoogleAuth\", ({ enumerable: true, get: function () { return google_auth_library_1.GoogleAuth; } }));\nObject.defineProperty(exports, \"ExternalAccountClient\", ({ enumerable: true, get: function () { return google_auth_library_1.ExternalAccountClient; } }));\nObject.defineProperty(exports, \"BaseExternalAccountClient\", ({ enumerable: true, get: function () { return google_auth_library_1.BaseExternalAccountClient; } }));\nObject.defineProperty(exports, \"IdentityPoolClient\", ({ enumerable: true, get: function () { return google_auth_library_1.IdentityPoolClient; } }));\nObject.defineProperty(exports, \"AwsClient\", ({ enumerable: true, get: function () { return google_auth_library_1.AwsClient; } }));\nvar gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nObject.defineProperty(exports, \"Gaxios\", ({ enumerable: true, get: function () { return gaxios_1.Gaxios; } }));\nObject.defineProperty(exports, \"GaxiosError\", ({ enumerable: true, get: function () { return gaxios_1.GaxiosError; } }));\nvar apiIndex_1 = __webpack_require__(/*! ./apiIndex */ \"(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\");\nObject.defineProperty(exports, \"getAPI\", ({ enumerable: true, get: function () { return apiIndex_1.getAPI; } }));\nvar apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nObject.defineProperty(exports, \"createAPIRequest\", ({ enumerable: true, get: function () { return apirequest_1.createAPIRequest; } }));\nvar authplus_1 = __webpack_require__(/*! ./authplus */ \"(rsc)/./node_modules/googleapis-common/build/src/authplus.js\");\nObject.defineProperty(exports, \"AuthPlus\", ({ enumerable: true, get: function () { return authplus_1.AuthPlus; } }));\nvar discovery_1 = __webpack_require__(/*! ./discovery */ \"(rsc)/./node_modules/googleapis-common/build/src/discovery.js\");\nObject.defineProperty(exports, \"Discovery\", ({ enumerable: true, get: function () { return discovery_1.Discovery; } }));\nvar endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nObject.defineProperty(exports, \"Endpoint\", ({ enumerable: true, get: function () { return endpoint_1.Endpoint; } }));\n__exportStar(__webpack_require__(/*! ./util */ \"(rsc)/./node_modules/googleapis-common/build/src/util.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/isbrowser.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isBrowser = isBrowser;\nfunction isBrowser() {\n    return typeof window !== 'undefined';\n}\n//# sourceMappingURL=isbrowser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29vZ2xlYXBpcy1jb21tb24vYnVpbGQvc3JjL2lzYnJvd3Nlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamF5a2VcXERvY3VtZW50c1xcU291cmNlIENvZGVcXHNpZ25cXG5vZGVfbW9kdWxlc1xcZ29vZ2xlYXBpcy1jb21tb25cXGJ1aWxkXFxzcmNcXGlzYnJvd3Nlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIENvcHlyaWdodCAyMDIwIEdvb2dsZSBMTENcbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4vLyB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4vLyBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbi8vXG4vLyAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5pc0Jyb3dzZXIgPSBpc0Jyb3dzZXI7XG5mdW5jdGlvbiBpc0Jyb3dzZXIoKSB7XG4gICAgcmV0dXJuIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXNicm93c2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/util.js":
/*!**********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/util.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2025 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.headersToClassicHeaders = headersToClassicHeaders;\nexports.marshallGaxiosResponse = marshallGaxiosResponse;\n/**\n * A utility for converting potential {@link Headers `Headers`} objects to plain headers objects.\n *\n * @param headers any compatible `HeadersInit` (`Headers`, (string, string)[], {})\n * @returns the headers in `Record<string, string>` form.\n */\nfunction headersToClassicHeaders(headers) {\n    let classicHeaders = {};\n    if (headers instanceof Headers) {\n        headers.forEach((value, key) => {\n            classicHeaders[key] = value;\n        });\n    }\n    else if (Array.isArray(headers)) {\n        for (const [key, value] of headers) {\n            classicHeaders[key] = value;\n        }\n    }\n    else {\n        classicHeaders = headers || {};\n    }\n    return classicHeaders;\n}\n/**\n * marshall a GaxiosResponse into a library-friendly type.\n *\n * @param res the Gaxios Response\n * @returns the GaxiosResponse with HTTP2-ready/compatible headers\n */\nfunction marshallGaxiosResponse(res) {\n    return Object.defineProperties(res || {}, {\n        headers: {\n            configurable: true,\n            writable: true,\n            enumerable: true,\n            value: headersToClassicHeaders(res?.headers),\n        },\n    });\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/package.json":
/*!*****************************************************!*\
  !*** ./node_modules/googleapis-common/package.json ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"googleapis-common","version":"8.0.0","description":"A common tooling library used by the googleapis npm module. You probably don\'t want to use this directly.","repository":"googleapis/nodejs-googleapis-common","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src","!build/src/**/*.map"],"scripts":{"prebenchmark":"npm run compile","benchmark":"node build/benchmark/bench.js","compile":"tsc -p .","test":"c8 mocha build/test","system-test":"c8 mocha build/system-test --timeout 600000","presystem-test":"npm run compile","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","lint":"gts check","samples-test":"mocha build/samples-test","docs":"jsdoc -c .jsdoc.js","predocs-test":"npm run docs","docs-test":"linkinator docs","webpack":"webpack","browser-test":"karma start","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"keywords":[],"author":"Google LLC","license":"Apache-2.0","dependencies":{"extend":"^3.0.2","gaxios":"^7.0.0-rc.4","google-auth-library":"^10.1.0","qs":"^6.7.0","url-template":"^2.0.8"},"devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@types/extend":"^3.0.1","@types/mocha":"^10.0.10","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/nock":"^11.0.0","@types/proxyquire":"^1.3.28","@types/qs":"^6.5.3","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/url-template":"^2.0.28","c8":"^10.1.3","codecov":"^3.5.0","gts":"^6.0.2","http2spy":"^2.0.0","is-docker":"^2.0.0","jsdoc":"^4.0.0","jsdoc-fresh":"^3.0.0","jsdoc-region-tag":"^3.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"^4.0.0","linkinator":"^6.1.2","mocha":"^11.1.0","mv":"^2.1.1","ncp":"^2.0.0","nock":"^14.0.1","null-loader":"^4.0.0","path-to-regexp":"^6.0.0","proxyquire":"^2.1.3","puppeteer":"^18.2.1","sinon":"^17.0.0","tmp":"^0.2.0","ts-loader":"^8.0.0","typescript":"^5.8.2","webpack":"^4.0.0","webpack-cli":"^4.0.0"},"engines":{"node":">=18.0.0"}}');

/***/ })

};
;