{"name": "sign", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate-types": "supabase gen types typescript --project-id YOUR_PROJECT_ID > src/types/supabase.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next-auth/supabase-adapter": "^0.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.52.1", "@types/multer": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "googleapis": "^154.0.0", "lucide-react": "^0.525.0", "multer": "^2.0.2", "next": "15.4.3", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}