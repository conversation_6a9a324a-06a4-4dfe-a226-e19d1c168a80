"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gcp-metadata";
exports.ids = ["vendor-chunks/gcp-metadata"];
exports.modules = {

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js":
/*!**************************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/gcp-residency.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GCE_LINUX_BIOS_PATHS = void 0;\nexports.isGoogleCloudServerless = isGoogleCloudServerless;\nexports.isGoogleComputeEngineLinux = isGoogleComputeEngineLinux;\nexports.isGoogleComputeEngineMACAddress = isGoogleComputeEngineMACAddress;\nexports.isGoogleComputeEngine = isGoogleComputeEngine;\nexports.detectGCPResidency = detectGCPResidency;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst os_1 = __webpack_require__(/*! os */ \"os\");\n/**\n * Known paths unique to Google Compute Engine Linux instances\n */\nexports.GCE_LINUX_BIOS_PATHS = {\n    BIOS_DATE: '/sys/class/dmi/id/bios_date',\n    BIOS_VENDOR: '/sys/class/dmi/id/bios_vendor',\n};\nconst GCE_MAC_ADDRESS_REGEX = /^42:01/;\n/**\n * Determines if the process is running on a Google Cloud Serverless environment (Cloud Run or Cloud Functions instance).\n *\n * Uses the:\n * - {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n * - {@link https://cloud.google.com/functions/docs/env-var Cloud Functions environment variables}.\n *\n * @returns {boolean} `true` if the process is running on GCP serverless, `false` otherwise.\n */\nfunction isGoogleCloudServerless() {\n    /**\n     * `CLOUD_RUN_JOB` is used for Cloud Run Jobs\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     *\n     * `FUNCTION_NAME` is used in older Cloud Functions environments:\n     * - See {@link https://cloud.google.com/functions/docs/env-var Python 3.7 and Go 1.11}.\n     *\n     * `K_SERVICE` is used in Cloud Run and newer Cloud Functions environments:\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     * - See {@link https://cloud.google.com/functions/docs/env-var Cloud Functions newer runtimes}.\n     */\n    const isGFEnvironment = process.env.CLOUD_RUN_JOB ||\n        process.env.FUNCTION_NAME ||\n        process.env.K_SERVICE;\n    return !!isGFEnvironment;\n}\n/**\n * Determines if the process is running on a Linux Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on Linux GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngineLinux() {\n    if ((0, os_1.platform)() !== 'linux')\n        return false;\n    try {\n        // ensure this file exist\n        (0, fs_1.statSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_DATE);\n        // ensure this file exist and matches\n        const biosVendor = (0, fs_1.readFileSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR, 'utf8');\n        return /Google/.test(biosVendor);\n    }\n    catch {\n        return false;\n    }\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance with a known\n * MAC address.\n *\n * @returns {boolean} `true` if the process is running on GCE (as determined by MAC address), `false` otherwise.\n */\nfunction isGoogleComputeEngineMACAddress() {\n    const interfaces = (0, os_1.networkInterfaces)();\n    for (const item of Object.values(interfaces)) {\n        if (!item)\n            continue;\n        for (const { mac } of item) {\n            if (GCE_MAC_ADDRESS_REGEX.test(mac)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngine() {\n    return isGoogleComputeEngineLinux() || isGoogleComputeEngineMACAddress();\n}\n/**\n * Determines if the process is running on Google Cloud Platform.\n *\n * @returns {boolean} `true` if the process is running on GCP, `false` otherwise.\n */\nfunction detectGCPResidency() {\n    return isGoogleCloudServerless() || isGoogleComputeEngine();\n}\n//# sourceMappingURL=gcp-residency.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/index.js":
/*!******************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.gcpResidencyCache = exports.METADATA_SERVER_DETECTION = exports.HEADERS = exports.HEADER_VALUE = exports.HEADER_NAME = exports.SECONDARY_HOST_ADDRESS = exports.HOST_ADDRESS = exports.BASE_PATH = void 0;\nexports.instance = instance;\nexports.project = project;\nexports.universe = universe;\nexports.bulk = bulk;\nexports.isAvailable = isAvailable;\nexports.resetIsAvailableCache = resetIsAvailableCache;\nexports.getGCPResidency = getGCPResidency;\nexports.setGCPResidency = setGCPResidency;\nexports.requestTimeout = requestTimeout;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nconst jsonBigint = __webpack_require__(/*! json-bigint */ \"(rsc)/./node_modules/json-bigint/index.js\");\nconst gcp_residency_1 = __webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\");\nconst logger = __importStar(__webpack_require__(/*! google-logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/index.js\"));\nexports.BASE_PATH = '/computeMetadata/v1';\nexports.HOST_ADDRESS = 'http://***************';\nexports.SECONDARY_HOST_ADDRESS = 'http://metadata.google.internal.';\nexports.HEADER_NAME = 'Metadata-Flavor';\nexports.HEADER_VALUE = 'Google';\nexports.HEADERS = Object.freeze({ [exports.HEADER_NAME]: exports.HEADER_VALUE });\nconst log = logger.log('gcp-metadata');\n/**\n * Metadata server detection override options.\n *\n * Available via `process.env.METADATA_SERVER_DETECTION`.\n */\nexports.METADATA_SERVER_DETECTION = Object.freeze({\n    'assume-present': \"don't try to ping the metadata server, but assume it's present\",\n    none: \"don't try to ping the metadata server, but don't try to use it either\",\n    'bios-only': \"treat the result of a BIOS probe as canonical (don't fall back to pinging)\",\n    'ping-only': 'skip the BIOS probe, and go straight to pinging',\n});\n/**\n * Returns the base URL while taking into account the GCE_METADATA_HOST\n * environment variable if it exists.\n *\n * @returns The base URL, e.g., http://***************/computeMetadata/v1.\n */\nfunction getBaseUrl(baseUrl) {\n    if (!baseUrl) {\n        baseUrl =\n            process.env.GCE_METADATA_IP ||\n                process.env.GCE_METADATA_HOST ||\n                exports.HOST_ADDRESS;\n    }\n    // If no scheme is provided default to HTTP:\n    if (!/^https?:\\/\\//.test(baseUrl)) {\n        baseUrl = `http://${baseUrl}`;\n    }\n    return new URL(exports.BASE_PATH, baseUrl).href;\n}\n// Accepts an options object passed from the user to the API. In previous\n// versions of the API, it referred to a `Request` or an `Axios` request\n// options object.  Now it refers to an object with very limited property\n// names. This is here to help ensure users don't pass invalid options when\n// they  upgrade from 0.4 to 0.5 to 0.8.\nfunction validate(options) {\n    Object.keys(options).forEach(key => {\n        switch (key) {\n            case 'params':\n            case 'property':\n            case 'headers':\n                break;\n            case 'qs':\n                throw new Error(\"'qs' is not a valid configuration option. Please use 'params' instead.\");\n            default:\n                throw new Error(`'${key}' is not a valid configuration option.`);\n        }\n    });\n}\nasync function metadataAccessor(type, options = {}, noResponseRetries = 3, fastFail = false) {\n    const headers = new Headers(exports.HEADERS);\n    let metadataKey = '';\n    let params = {};\n    if (typeof type === 'object') {\n        const metadataAccessor = type;\n        new Headers(metadataAccessor.headers).forEach((value, key) => headers.set(key, value));\n        metadataKey = metadataAccessor.metadataKey;\n        params = metadataAccessor.params || params;\n        noResponseRetries = metadataAccessor.noResponseRetries || noResponseRetries;\n        fastFail = metadataAccessor.fastFail || fastFail;\n    }\n    else {\n        metadataKey = type;\n    }\n    if (typeof options === 'string') {\n        metadataKey += `/${options}`;\n    }\n    else {\n        validate(options);\n        if (options.property) {\n            metadataKey += `/${options.property}`;\n        }\n        new Headers(options.headers).forEach((value, key) => headers.set(key, value));\n        params = options.params || params;\n    }\n    const requestMethod = fastFail ? fastFailMetadataRequest : gaxios_1.request;\n    const req = {\n        url: `${getBaseUrl()}/${metadataKey}`,\n        headers,\n        retryConfig: { noResponseRetries },\n        params,\n        responseType: 'text',\n        timeout: requestTimeout(),\n    };\n    log.info('instance request %j', req);\n    const res = await requestMethod(req);\n    log.info('instance metadata is %s', res.data);\n    const metadataFlavor = res.headers.get(exports.HEADER_NAME);\n    if (metadataFlavor !== exports.HEADER_VALUE) {\n        throw new RangeError(`Invalid response from metadata service: incorrect ${exports.HEADER_NAME} header. Expected '${exports.HEADER_VALUE}', got ${metadataFlavor ? `'${metadataFlavor}'` : 'no header'}`);\n    }\n    if (typeof res.data === 'string') {\n        try {\n            return jsonBigint.parse(res.data);\n        }\n        catch {\n            /* ignore */\n        }\n    }\n    return res.data;\n}\nasync function fastFailMetadataRequest(options) {\n    const secondaryOptions = {\n        ...options,\n        url: options.url\n            ?.toString()\n            .replace(getBaseUrl(), getBaseUrl(exports.SECONDARY_HOST_ADDRESS)),\n    };\n    // We race a connection between DNS/IP to metadata server. There are a couple\n    // reasons for this:\n    //\n    // 1. the DNS is slow in some GCP environments; by checking both, we might\n    //    detect the runtime environment significantly faster.\n    // 2. we can't just check the IP, which is tarpitted and slow to respond\n    //    on a user's local machine.\n    //\n    // Returns first resolved promise or if all promises get rejected we return an AggregateError.\n    //\n    // Note, however, if a failure happens prior to a success, a rejection should\n    // occur, this is for folks running locally.\n    //\n    const r1 = (0, gaxios_1.request)(options);\n    const r2 = (0, gaxios_1.request)(secondaryOptions);\n    return Promise.any([r1, r2]);\n}\n/**\n * Obtain metadata for the current GCE instance.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const serviceAccount: {} = await instance('service-accounts/');\n * const serviceAccountEmail: string = await instance('service-accounts/default/email');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction instance(options) {\n    return metadataAccessor('instance', options);\n}\n/**\n * Obtain metadata for the current GCP project.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const projectId: string = await project('project-id');\n * const numericProjectId: number = await project('numeric-project-id');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction project(options) {\n    return metadataAccessor('project', options);\n}\n/**\n * Obtain metadata for the current universe.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const universeDomain: string = await universe('universe-domain');\n * ```\n */\nfunction universe(options) {\n    return metadataAccessor('universe', options);\n}\n/**\n * Retrieve metadata items in parallel.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const data = await bulk([\n *   {\n *     metadataKey: 'instance',\n *   },\n *   {\n *     metadataKey: 'project/project-id',\n *   },\n * ] as const);\n *\n * // data.instance;\n * // data['project/project-id'];\n * ```\n *\n * @param properties The metadata properties to retrieve\n * @returns The metadata in `metadatakey:value` format\n */\nasync function bulk(properties) {\n    const r = {};\n    await Promise.all(properties.map(item => {\n        return (async () => {\n            const res = await metadataAccessor(item);\n            const key = item.metadataKey;\n            r[key] = res;\n        })();\n    }));\n    return r;\n}\n/*\n * How many times should we retry detecting GCP environment.\n */\nfunction detectGCPAvailableRetries() {\n    return process.env.DETECT_GCP_RETRIES\n        ? Number(process.env.DETECT_GCP_RETRIES)\n        : 0;\n}\nlet cachedIsAvailableResponse;\n/**\n * Determine if the metadata server is currently available.\n */\nasync function isAvailable() {\n    if (process.env.METADATA_SERVER_DETECTION) {\n        const value = process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();\n        if (!(value in exports.METADATA_SERVER_DETECTION)) {\n            throw new RangeError(`Unknown \\`METADATA_SERVER_DETECTION\\` env variable. Got \\`${value}\\`, but it should be \\`${Object.keys(exports.METADATA_SERVER_DETECTION).join('`, `')}\\`, or unset`);\n        }\n        switch (value) {\n            case 'assume-present':\n                return true;\n            case 'none':\n                return false;\n            case 'bios-only':\n                return getGCPResidency();\n            case 'ping-only':\n            // continue, we want to ping the server\n        }\n    }\n    try {\n        // If a user is instantiating several GCP libraries at the same time,\n        // this may result in multiple calls to isAvailable(), to detect the\n        // runtime environment. We use the same promise for each of these calls\n        // to reduce the network load.\n        if (cachedIsAvailableResponse === undefined) {\n            cachedIsAvailableResponse = metadataAccessor('instance', undefined, detectGCPAvailableRetries(), \n            // If the default HOST_ADDRESS has been overridden, we should not\n            // make an effort to try SECONDARY_HOST_ADDRESS (as we are likely in\n            // a non-GCP environment):\n            !(process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST));\n        }\n        await cachedIsAvailableResponse;\n        return true;\n    }\n    catch (e) {\n        const err = e;\n        if (process.env.DEBUG_AUTH) {\n            console.info(err);\n        }\n        if (err.type === 'request-timeout') {\n            // If running in a GCP environment, metadata endpoint should return\n            // within ms.\n            return false;\n        }\n        if (err.response && err.response.status === 404) {\n            return false;\n        }\n        else {\n            if (!(err.response && err.response.status === 404) &&\n                // A warning is emitted if we see an unexpected err.code, or err.code\n                // is not populated:\n                (!err.code ||\n                    ![\n                        'EHOSTDOWN',\n                        'EHOSTUNREACH',\n                        'ENETUNREACH',\n                        'ENOENT',\n                        'ENOTFOUND',\n                        'ECONNREFUSED',\n                    ].includes(err.code.toString()))) {\n                let code = 'UNKNOWN';\n                if (err.code)\n                    code = err.code.toString();\n                process.emitWarning(`received unexpected error = ${err.message} code = ${code}`, 'MetadataLookupWarning');\n            }\n            // Failure to resolve the metadata service means that it is not available.\n            return false;\n        }\n    }\n}\n/**\n * reset the memoized isAvailable() lookup.\n */\nfunction resetIsAvailableCache() {\n    cachedIsAvailableResponse = undefined;\n}\n/**\n * A cache for the detected GCP Residency.\n */\nexports.gcpResidencyCache = null;\n/**\n * Detects GCP Residency.\n * Caches results to reduce costs for subsequent calls.\n *\n * @see setGCPResidency for setting\n */\nfunction getGCPResidency() {\n    if (exports.gcpResidencyCache === null) {\n        setGCPResidency();\n    }\n    return exports.gcpResidencyCache;\n}\n/**\n * Sets the detected GCP Residency.\n * Useful for forcing metadata server detection behavior.\n *\n * Set `null` to autodetect the environment (default behavior).\n * @see getGCPResidency for getting\n */\nfunction setGCPResidency(value = null) {\n    exports.gcpResidencyCache = value !== null ? value : (0, gcp_residency_1.detectGCPResidency)();\n}\n/**\n * Obtain the timeout for requests to the metadata server.\n *\n * In certain environments and conditions requests can take longer than\n * the default timeout to complete. This function will determine the\n * appropriate timeout based on the environment.\n *\n * @returns {number} a request timeout duration in milliseconds.\n */\nfunction requestTimeout() {\n    return getGCPResidency() ? 0 : 3000;\n}\n__exportStar(__webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/index.js\n");

/***/ })

};
;