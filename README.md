# Document Signing System

A comprehensive document signing and management system built with Next.js, featuring user and admin dashboards, Google Drive integration, and automated Google Sheets tracking.

## Features

### 🔐 Authentication & Authorization
- Google OAuth integration
- Role-based access control (User/Admin)
- Secure session management with NextAuth.js

### 📄 Document Management
- Secure file upload with validation
- Support for PDF, DOC, DOCX, and image formats
- Metadata capture (subject, details, urgency levels)
- Real-time status tracking

### 👤 User Dashboard
- Upload documents for signing
- Track document status (Pending, Signed, Feedback)
- Download signed documents
- View feedback from admins

### 🛠️ Admin Dashboard
- Review all uploaded documents
- Filter by status and urgency
- Provide feedback to users
- Upload signed documents
- Comprehensive document management

### ☁️ Google Services Integration
- **Google Drive**: Automatic file storage and organization
- **Google Sheets**: Automated tracking and reporting
- **Google Apps Script**: Real-time sheet updates

### 📊 Analytics & Tracking
- Document statistics dashboard
- Status tracking and reporting
- Urgency level management
- Automated notifications

## Tech Stack

- **Frontend**: Next.js 14, React, <PERSON>Script, Tailwind CSS
- **Authentication**: NextAuth.js with Google OAuth
- **Database**: Supabase (PostgreSQL)
- **File Storage**: Google Drive API
- **Spreadsheet**: Google Sheets API
- **UI Components**: Radix UI, Lucide React
- **Deployment**: Vercel

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd sign
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Fill in your configuration values
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open [http://localhost:3000](http://localhost:3000)**

## Detailed Setup

For complete setup instructions including Google Cloud Console, Supabase, and Google Sheets configuration, see [SETUP.md](./SETUP.md).

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # User dashboard
│   └── admin/             # Admin dashboard
├── components/            # React components
│   ├── ui/               # UI components
│   └── upload-modal.tsx  # File upload modal
├── lib/                  # Utility libraries
│   ├── auth.ts          # Authentication config
│   ├── google.ts        # Google APIs integration
│   ├── supabase.ts      # Database client
│   └── utils.ts         # Helper functions
└── types/               # TypeScript type definitions
```

## Key Features Explained

### Document Workflow
1. **User uploads** document with metadata
2. **System stores** file in Google Drive
3. **Google Sheets** automatically updated
4. **Admin reviews** and either signs or provides feedback
5. **User receives** signed document or feedback
6. **Status tracking** throughout the process

### Security Features
- Row Level Security (RLS) in Supabase
- Protected API routes with authentication
- File type and size validation
- Role-based access control
- Secure file storage in Google Drive

### Google Integration
- **Drive API**: Secure file storage with proper permissions
- **Sheets API**: Automated tracking and reporting
- **Apps Script**: Real-time updates and notifications

## Environment Variables

See `.env.example` for all required environment variables including:
- Supabase configuration
- Google OAuth credentials
- Google Service Account details
- NextAuth configuration

## Deployment

The application is designed to be deployed on Vercel:

1. Push to GitHub
2. Connect repository to Vercel
3. Configure environment variables
4. Deploy automatically

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For setup help and troubleshooting, see [SETUP.md](./SETUP.md) or create an issue in the repository.
